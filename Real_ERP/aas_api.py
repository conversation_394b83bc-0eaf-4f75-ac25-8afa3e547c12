# -*- coding: utf-8 -*-
from odoo import http
from odoo.http import request
from datetime import datetime
import json

import logging
_logger = logging.getLogger(__name__)

class ZemaAasViewerAPI(http.Controller):
    """
    AAS (Asset Administration Shell) API Controller
    Provides endpoints for generating AAS templates and nameplate data from Odoo ERP
    """

    #region Main AAS Template Endpoints

    @http.route(['/api/sai/aas/template', '/api/aas/template'], type='json', website=False, auth='public')
    def get_product_aas_template_simple(self, **kw):
        """Get AAS template with real ERP data - mimics mock server format"""
        try:
            # Get product_id from request parameters
            product_id = kw.get('product_id')
            if not product_id:
                return {
                    "status": "error",
                    "message": "product_id parameter is required"
                }

            # Log debug information
            _logger.info(f"=== AAS Template API Debug ===")
            _logger.info(f"Received product_id: {product_id}")
            _logger.info(f"Request parameters: {kw}")

            # Fetch product from Odoo ERP
            product = request.env['product.product'].sudo().browse(int(product_id))
            if not product.exists():
                return {
                    "status": "error",
                    "message": f"Product with ID {product_id} not found"
                }

            # Get basic product info from ERP
            manufacturer_name = product.manufacturer.name if product.manufacturer else "Unknown Manufacturer"
            product_name = product.name or "Unknown Product"
            serial_number = product.default_code or f"SN-{product.id}"
            year_of_construction = str(product.create_date.year) if product.create_date else str(datetime.now().year)
            product_family = getattr(product, 'manufacturer_product_family', None) or product.categ_id.name or "Unknown Family"

            # Get manufacturer address
            address_data = {
                "street": "Unknown Street",
                "zipcode": "00000",
                "city": "Unknown City",
                "state": "Unknown State",
                "country": "XX"
            }

            contact_email = "<EMAIL>"
            contact_phone = "No phone available"

            if product.manufacturer:
                address_data = {
                    "street": product.manufacturer.street or "Unknown Street",
                    "zipcode": product.manufacturer.zip or "00000",
                    "city": product.manufacturer.city or "Unknown City",
                    "state": product.manufacturer.state_id.name if product.manufacturer.state_id else "Unknown State",
                    "country": product.manufacturer.country_id.code if product.manufacturer.country_id else "XX"
                }
                contact_email = product.manufacturer.email or "<EMAIL>"
                contact_phone = product.manufacturer.phone or "No phone available"

            # Get technical specifications from product attributes
            technical_specs = {}
            if hasattr(product, 'combination_indices') and product.combination_indices:
                attribute_indices = product.combination_indices.split(",")
                for indice in attribute_indices:
                    try:
                        product_template_attribute_value = request.env['product.template.attribute.value'].sudo().search([('id','=',int(indice))])
                        if product_template_attribute_value:
                            attr_name = product_template_attribute_value.attribute_id.name
                            attr_value = product_template_attribute_value.product_attribute_value_id.name
                            technical_specs[attr_name] = attr_value
                    except:
                        continue

            # Add basic product specs
            if product.lst_price:
                technical_specs["price"] = f"{product.lst_price}"
            if product.weight:
                technical_specs["weight"] = f"{product.weight} kg"
            if product.barcode:
                technical_specs["barcode"] = product.barcode

            # Generate unique AAS ID
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            clean_manufacturer = manufacturer_name.replace(" ", "").replace(".", "").replace("-", "")
            aas_id = f"https://{clean_manufacturer.lower()}.com/aas/{product_name.replace(' ', '_')}_{serial_number}_{timestamp}"
            submodel_id = aas_id.replace('/aas/', '/sm/') + "/DigitalNameplate"

            # Create response in same format as mock server
            aas_template = {
                "aas": {
                    "modelType": "AssetAdministrationShell",
                    "id": aas_id,
                    "idShort": f"{product_name.replace(' ', '_')}_{serial_number}",
                    "description": [
                        {
                            "language": "en",
                            "text": f"Digital Nameplate AAS for {product_name} {serial_number}"
                        },
                        {
                            "language": "de",
                            "text": f"Digitales Typenschild AAS für {product_name} {serial_number}"
                        }
                    ],
                    "administration": {
                        "version": "1",
                        "revision": "0"
                    },
                    "assetInformation": {
                        "assetKind": "Instance",
                        "assetType": "Instance",
                        "globalAssetId": aas_id + "_asset"
                    },
                    "submodels": [
                        {
                            "keys": [
                                {
                                    "type": "Submodel",
                                    "value": submodel_id
                                }
                            ],
                            "type": "ModelReference"
                        }
                    ]
                },
                "submodel": {
                    "modelType": "Submodel",
                    "kind": "Instance",
                    "semanticId": {
                        "keys": [{
                            "type": "Submodel",
                            "value": "https://admin-shell.io/zvei/nameplate/1/0/Nameplate"
                        }],
                        "type": "ModelReference"
                    },
                    "id": submodel_id,
                    "idShort": "DigitalNameplate",
                    "submodelElements": self._create_nameplate_elements(manufacturer_name, product_name, serial_number, year_of_construction, address_data, contact_email, contact_phone, technical_specs)
                },
                "erp_data": {
                    "manufacturerName": {
                        "en": manufacturer_name,
                        "de": manufacturer_name
                    },
                    "productDesignation": f"{product_name}",
                    "productFamily": product_family,
                    "serialNumber": serial_number,
                    "yearOfConstruction": year_of_construction,
                    "address": address_data,
                    "email": contact_email,
                    "phone": contact_phone,
                    "technicalSpecs": technical_specs
                }
            }

            return {
                "status": "success",
                "template": aas_template,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            _logger.error(f"Error generating AAS template for product {product_id}: {str(e)}")
            return {
                "status": "error",
                "message": str(e),
                "timestamp": datetime.now().isoformat()
            }

    @http.route(['/api/sai/aas/template/http', '/api/aas/template/http'], type='http', auth='public', methods=['GET', 'POST'])
    def get_product_aas_template_http(self, product_id=None, **kw):
        """HTTP version of AAS template endpoint for easier Node-RED integration"""
        try:
            # Get product_id from URL parameter or POST data
            if not product_id:
                product_id = kw.get('product_id')

            if not product_id:
                return request.make_response(
                    json.dumps({
                        "status": "error",
                        "message": "product_id parameter is required"
                    }),
                    headers=[('Content-Type', 'application/json')]
                )

            # Log debug information
            _logger.info(f"=== AAS Template HTTP API Debug ===")
            _logger.info(f"Received product_id: {product_id}")
            _logger.info(f"Request method: {request.httprequest.method}")
            _logger.info(f"Request parameters: {kw}")

            # Call the JSON version and return as HTTP response
            result = self.get_product_aas_template_simple(product_id=product_id)

            return request.make_response(
                json.dumps(result),
                headers=[('Content-Type', 'application/json')]
            )

        except Exception as e:
            _logger.error(f"Error in HTTP AAS template endpoint: {str(e)}")
            return request.make_response(
                json.dumps({
                    "status": "error",
                    "message": str(e),
                    "timestamp": datetime.now().isoformat()
                }),
                headers=[('Content-Type', 'application/json')]
            )

    #endregion

    #region Nameplate Data Endpoints (JSON)

    @http.route('/api/aas/nameplate/<int:product_id>/json', type='json', website=False, auth='public')
    def get_nameplate_json(self, product_id):
        """Get product Digital Nameplate data following IDTA standards from ERP"""
        try:
            # Fetch product from ERP
            product = request.env['product.product'].sudo().browse(product_id)
            if not product.exists():
                return {
                    "status": "error",
                    "message": f"Product with ID {product_id} not found",
                    "timestamp": datetime.now().isoformat()
                }

            # Map ERP data to nameplate format
            nameplate_data = self._map_product_to_nameplate(product)

            return {
                "status": "success",
                "nameplate": nameplate_data,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            _logger.error(f"Error fetching nameplate for product {product_id}: {str(e)}")
            return {
                "status": "error",
                "message": str(e),
                "timestamp": datetime.now().isoformat()
            }

    @http.route('/api/sai/products/<int:product_id>/aas-template', type='json', website=False, auth='public')
    def get_product_aas_template(self, product_id):
        """Get AAS-ready template with proper semantic IDs following IDTA standards from ERP"""
        try:
            # Fetch product from ERP
            product = request.env['product.product'].sudo().browse(product_id)
            if not product.exists():
                return {
                    "status": "error",
                    "message": f"Product with ID {product_id} not found",
                    "timestamp": datetime.now().isoformat()
                }

            # Generate AAS template with ERP data
            aas_template = self._generate_aas_template(product)

            return {
                "status": "success",
                "template": aas_template,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            _logger.error(f"Error generating AAS template for product {product_id}: {str(e)}")
            return {
                "status": "error",
                "message": str(e),
                "timestamp": datetime.now().isoformat()
            }

    #endregion

    #region Helper Methods

    def _map_product_to_nameplate(self, product):
        """Map Odoo product data to Digital Nameplate format"""

        # Get manufacturer info
        manufacturer_name = product.manufacturer.name if product.manufacturer else "Unknown Manufacturer"

        # Get product designation
        product_designation = product.name or "Unknown Product"

        # Get product family
        product_family = getattr(product, 'manufacturer_product_family', None) or product.categ_id.name or "Unknown Family"

        # Get serial number (using default_code as serial number)
        serial_number = product.default_code or f"SN-{product.id}"

        # Get year of construction (using create_date year or current year)
        year_of_construction = str(product.create_date.year) if product.create_date else str(datetime.now().year)

        # Get manufacturer address info
        address_data = {}
        email_data = {}
        phone_data = {}

        if product.manufacturer:
            address_data = {
                "street": product.manufacturer.street or "Unknown Street",
                "zipcode": product.manufacturer.zip or "00000",
                "city": product.manufacturer.city or "Unknown City",
                "state": product.manufacturer.state_id.name if product.manufacturer.state_id else "Unknown State",
                "country": product.manufacturer.country_id.code if product.manufacturer.country_id else "XX"
            }
            email_data = {
                "address": product.manufacturer.email or "<EMAIL>"
            }
            phone_data = {
                "number": product.manufacturer.phone or "No phone available"
            }

        # Get technical specifications from product attributes
        technical_specs = self._get_technical_specifications(product)

        nameplate_data = {
            "manufacturerName": [
                {"language": "en", "text": manufacturer_name},
                {"language": "de", "text": manufacturer_name}
            ],
            "manufacturerProductDesignation": [
                {"language": "en", "text": product_designation},
                {"language": "de", "text": product_designation}
            ],
            "manufacturerProductFamily": [
                {"language": "en", "text": product_family},
                {"language": "de", "text": product_family}
            ],
            "serialNumber": serial_number,
            "productArticleNumberOfManufacturer": product.barcode or product.default_code or f"ART-{product.id}",
            "contactInformation": f"{manufacturer_name} | {email_data.get('address', '<EMAIL>')} | {phone_data.get('number', 'No phone available')}",
            "yearOfConstruction": year_of_construction,
            "address": address_data,
            "email": email_data,
            "phone": phone_data,
            "technicalSpecifications": technical_specs
        }

        return nameplate_data

    def _get_technical_specifications(self, product):
        """Extract technical specifications from product attributes"""
        technical_specs = {}

        # Get product attributes if available
        if hasattr(product, 'combination_indices') and product.combination_indices:
            attribute_indices = product.combination_indices.split(",")
            for indice in attribute_indices:
                try:
                    product_template_attribute_value = request.env['product.template.attribute.value'].sudo().search([('id','=',int(indice))])
                    if product_template_attribute_value:
                        attr_name = product_template_attribute_value.attribute_id.name
                        attr_value = product_template_attribute_value.product_attribute_value_id.name
                        technical_specs[attr_name] = attr_value
                except:
                    continue

        # Add basic product info as technical specs
        if product.lst_price:
            technical_specs["price"] = f"{product.lst_price} {product.currency_id.name if product.currency_id else 'EUR'}"

        if product.weight:
            technical_specs["weight"] = f"{product.weight} kg"

        if product.volume:
            technical_specs["volume"] = f"{product.volume} m³"

        # Add barcode if available
        if product.barcode:
            technical_specs["barcode"] = product.barcode

        return technical_specs

    def _generate_aas_template(self, product):
        """Generate complete AAS template with ERP data"""

        # Generate unique AAS ID
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        serial_number = product.default_code or f"SN-{product.id}"
        manufacturer_name = product.manufacturer.name if product.manufacturer else "UnknownManufacturer"

        # Clean manufacturer name for ID
        clean_manufacturer = manufacturer_name.replace(" ", "").replace(".", "").replace("-", "")

        aas_id = f"https://{clean_manufacturer.lower()}.com/aas/{product.name.replace(' ', '_')}_{serial_number}_{timestamp}"
        submodel_id = aas_id.replace('/aas/', '/sm/') + "/DigitalNameplate"

        # Get nameplate data
        nameplate_data = self._map_product_to_nameplate(product)

        aas_template = {
            "aas": {
                "modelType": "AssetAdministrationShell",
                "id": aas_id,
                "idShort": f"{product.name.replace(' ', '_')}_{serial_number}",
                "description": [
                    {
                        "language": "en",
                        "text": f"Digital Nameplate AAS for {product.name} {serial_number}"
                    },
                    {
                        "language": "de",
                        "text": f"Digitales Typenschild AAS für {product.name} {serial_number}"
                    }
                ],
                "administration": {
                    "version": "1",
                    "revision": "0"
                },
                "assetInformation": {
                    "assetKind": "Instance",
                    "assetType": "Instance",
                    "globalAssetId": aas_id + "_asset"
                },
                "submodels": [
                    {
                        "keys": [
                            {
                                "type": "Submodel",
                                "value": submodel_id
                            }
                        ],
                        "type": "ModelReference"
                    }
                ]
            },
            "submodel": {
                "modelType": "Submodel",
                "kind": "Instance",
                "semanticId": {
                    "keys": [{
                        "type": "Submodel",
                        "value": "https://admin-shell.io/zvei/nameplate/1/0/Nameplate"
                    }],
                    "type": "ModelReference"
                },
                "id": submodel_id,
                "idShort": "DigitalNameplate",
                "submodelElements": []  # Will be populated by mapping logic
            },
            "erp_data": {
                "manufacturerName": nameplate_data["manufacturerName"][0]["text"] if nameplate_data["manufacturerName"] else "Unknown",
                "productDesignation": nameplate_data["manufacturerProductDesignation"][0]["text"] if nameplate_data["manufacturerProductDesignation"] else "Unknown",
                "productFamily": nameplate_data["manufacturerProductFamily"][0]["text"] if nameplate_data["manufacturerProductFamily"] else "Unknown",
                "serialNumber": nameplate_data["serialNumber"],
                "yearOfConstruction": nameplate_data["yearOfConstruction"],
                "address": nameplate_data["address"],
                "email": nameplate_data["email"]["address"] if nameplate_data["email"] else "<EMAIL>",
                "phone": nameplate_data["phone"]["number"] if nameplate_data["phone"] else "No phone available",
                "technicalSpecs": nameplate_data["technicalSpecifications"]
            }
        }

        return aas_template

    #endregion

    #region Product Listing Endpoints

    @http.route('/api/aas/products', type='json', website=False, auth='public')
    def get_all_products(self):
        """Get all products with basic info for AAS generation"""
        try:
            products = request.env['product.product'].sudo().search([])
            response = []

            for product in products:
                manufacturer_name = product.manufacturer.name if product.manufacturer else "Unknown Manufacturer"
                serial_number = product.default_code or f"SN-{product.id}"

                response.append({
                    'id': product.id,
                    'name': product.name,
                    'SerialNumber': serial_number,
                    'ManufacturerName': manufacturer_name,
                    'ManufacturerProductDesignation': product.name,
                    'ManufacturerProductFamily': getattr(product, 'manufacturer_product_family', None) or product.categ_id.name,
                    'ProductArticleNumberOfManufacturer': product.barcode or '',
                    'TotalCosts': product.lst_price,
                    'ProductCategory': product.categ_id.name,
                    'api_endpoints': {
                        'nameplate_json': f'/api/aas/nameplate/{product.id}/json',
                        'nameplate_template': f'/api/aas/nameplate/{product.id}'
                    }
                })

            return {
                "status": "success",
                "products": response,
                "count": len(response),
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            _logger.error(f"Error fetching products: {str(e)}")
            return {
                "status": "error",
                "message": str(e),
                "timestamp": datetime.now().isoformat()
            }

    #endregion

    #region Utility Endpoints

    @http.route('/api/aas/health', type='json', website=False, auth='public')
    def health_check(self):
        """Health check endpoint"""
        return {
            "status": "healthy",
            "service": "Zema AAS Viewer API",
            "timestamp": datetime.now().isoformat()
        }

    @http.route('/api/aas/nameplate/<int:product_id>', type='http', auth='public', methods=['GET', 'POST'])
    def get_aas_nameplate_template(self, product_id):
        """
        Get AAS Digital Nameplate Template
        Returns AAS template with nameplate data only (no technical specifications)
        URL: /api/aas/nameplate/{product_id}
        """
        try:
            product = request.env['product.product'].sudo().browse(product_id)
            if not product.exists():
                return request.not_found()

            # Get basic product info from ERP
            manufacturer_name = product.manufacturer.name if product.manufacturer else "Unknown Manufacturer"
            product_name = product.name or "Unknown Product"
            serial_number = product.default_code or f"SN-{product.id}"
            year_of_construction = str(product.create_date.year) if product.create_date else str(datetime.now().year)
            product_family = getattr(product, 'manufacturer_product_family', None) or product.categ_id.name or "Unknown Family"

            # Get manufacturer address
            address_data = {
                "street": "Unknown Street",
                "zipcode": "00000",
                "city": "Unknown City",
                "state": "Unknown State",
                "country": "XX"
            }

            contact_email = "<EMAIL>"
            contact_phone = "No phone available"

            if product.manufacturer:
                address_data = {
                    "street": product.manufacturer.street or "Unknown Street",
                    "zipcode": product.manufacturer.zip or "00000",
                    "city": product.manufacturer.city or "Unknown City",
                    "state": product.manufacturer.state_id.name if product.manufacturer.state_id else "Unknown State",
                    "country": product.manufacturer.country_id.code if product.manufacturer.country_id else "XX"
                }
                contact_email = product.manufacturer.email or "<EMAIL>"
                contact_phone = product.manufacturer.phone or "No phone available"

            # Skip technical specifications - only nameplate data needed

            # Generate unique AAS ID
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            clean_manufacturer = manufacturer_name.replace(" ", "").replace(".", "").replace("-", "")
            aas_id = f"https://{clean_manufacturer.lower()}.com/aas/{product_name.replace(' ', '_')}_{serial_number}_{timestamp}"
            submodel_id = aas_id.replace('/aas/', '/sm/') + "/DigitalNameplate"

            # Create response in same format as mock server
            aas_template = {
                "aas": {
                    "modelType": "AssetAdministrationShell",
                    "id": aas_id,
                    "idShort": f"{product_name.replace(' ', '_')}_{serial_number}",
                    "description": [
                        {
                            "language": "en",
                            "text": f"Digital Nameplate AAS for {product_name} {serial_number}"
                        },
                        {
                            "language": "de",
                            "text": f"Digitales Typenschild AAS für {product_name} {serial_number}"
                        }
                    ],
                    "administration": {
                        "version": "1",
                        "revision": "0"
                    },
                    "assetInformation": {
                        "assetKind": "Instance",
                        "assetType": "Instance",
                        "globalAssetId": aas_id + "_asset"
                    },
                    "submodels": [
                        {
                            "keys": [
                                {
                                    "type": "Submodel",
                                    "value": submodel_id
                                }
                            ],
                            "type": "ModelReference"
                        }
                    ]
                },
                "submodel": {
                    "modelType": "Submodel",
                    "kind": "Instance",
                    "semanticId": {
                        "keys": [{
                            "type": "Submodel",
                            "value": "https://admin-shell.io/zvei/nameplate/1/0/Nameplate"
                        }],
                        "type": "ModelReference"
                    },
                    "id": submodel_id,
                    "idShort": "DigitalNameplate",
                    "submodelElements": []
                },
                "erp_data": {
                    "manufacturerName": {
                        "en": manufacturer_name,
                        "de": manufacturer_name
                    },
                    "productDesignation": f"{product_name}",
                    "productFamily": product_family,
                    "serialNumber": serial_number,
                    "productArticleNumberOfManufacturer": product.barcode or product.default_code or f"ART-{product.id}",
                    "contactInformation": f"{manufacturer_name} | {contact_email} | {contact_phone}",
                    "yearOfConstruction": year_of_construction,
                    "address": address_data,
                    "email": contact_email,
                    "phone": contact_phone
                }
            }

            response_data = {
                "status": "success",
                "template": aas_template,
                "timestamp": datetime.now().isoformat()
            }

            return request.make_response(
                json.dumps(response_data),
                headers=[('Content-Type', 'application/json')]
            )

        except Exception as e:
            _logger.error(f"Error generating AAS template for product {product_id}: {str(e)}")
            error_response = {
                "status": "error",
                "message": str(e),
                "timestamp": datetime.now().isoformat()
            }
            return request.make_response(
                json.dumps(error_response),
                headers=[('Content-Type', 'application/json')]
            )

    @http.route('/api/products/list', type='json', website=False, auth='public')
    def get_products_simple(self):
        """Get all products - simple format for selection"""
        try:
            products = request.env['product.product'].sudo().search([])
            response = []

            for product in products:
                manufacturer_name = product.manufacturer.name if product.manufacturer else "Unknown Manufacturer"
                serial_number = product.default_code or f"SN-{product.id}"

                response.append({
                    'id': product.id,
                    'name': product.name,
                    'manufacturer': manufacturer_name,
                    'serial_number': serial_number,
                    'category': product.categ_id.name,
                    'aas_endpoint': f'/api/products/{product.id}/aas-template'
                })

            return {
                "status": "success",
                "products": response,
                "count": len(response)
            }

        except Exception as e:
            _logger.error(f"Error fetching products: {str(e)}")
            return {
                "status": "error",
                "message": str(e)
            }

    #endregion

    def _create_nameplate_elements(self, manufacturer_name, product_name, serial_number, year_of_construction, address_data, contact_email, contact_phone, technical_specs):
        """
        Create standard IDTA Digital Nameplate submodel elements
        Based on IDTA-02002-1-0 Digital Nameplate specification
        """
        elements = []

        # URIOfTheProduct (0173-1#02-AAY811#001)
        elements.append({
            "modelType": "Property",
            "kind": "Instance",
            "semanticId": {
                "keys": [{
                    "type": "GlobalReference",
                    "value": "0173-1#02-AAY811#001"
                }],
                "type": "ModelReference"
            },
            "idShort": "URIOfTheProduct",
            "valueType": "xs:string",
            "value": f"https://{manufacturer_name.replace(' ', '').lower()}.com/products/{serial_number}"
        })

        # ManufacturerName (0173-1#02-AAO677#002)
        elements.append({
            "modelType": "MultiLanguageProperty",
            "kind": "Instance",
            "semanticId": {
                "keys": [{
                    "type": "GlobalReference",
                    "value": "0173-1#02-AAO677#002"
                }],
                "type": "ModelReference"
            },
            "idShort": "ManufacturerName",
            "value": [
                {
                    "language": "en",
                    "text": manufacturer_name
                },
                {
                    "language": "de",
                    "text": manufacturer_name
                }
            ]
        })

        # ManufacturerProductDesignation (0173-1#02-AAW338#001)
        elements.append({
            "modelType": "MultiLanguageProperty",
            "kind": "Instance",
            "semanticId": {
                "keys": [{
                    "type": "GlobalReference",
                    "value": "0173-1#02-AAW338#001"
                }],
                "type": "ModelReference"
            },
            "idShort": "ManufacturerProductDesignation",
            "value": [
                {
                    "language": "en",
                    "text": product_name
                },
                {
                    "language": "de",
                    "text": product_name
                }
            ]
        })

        # SerialNumber (0173-1#02-AAM556#002)
        elements.append({
            "modelType": "Property",
            "kind": "Instance",
            "semanticId": {
                "keys": [{
                    "type": "GlobalReference",
                    "value": "0173-1#02-AAM556#002"
                }],
                "type": "ModelReference"
            },
            "idShort": "SerialNumber",
            "valueType": "xs:string",
            "value": serial_number
        })

        # YearOfConstruction (0173-1#02-AAP906#001)
        elements.append({
            "modelType": "Property",
            "kind": "Instance",
            "semanticId": {
                "keys": [{
                    "type": "GlobalReference",
                    "value": "0173-1#02-AAP906#001"
                }],
                "type": "ModelReference"
            },
            "idShort": "YearOfConstruction",
            "valueType": "xs:gYear",
            "value": year_of_construction
        })

        # ContactInformation SubmodelElementCollection (0173-1#02-AAQ837#005)
        contact_elements = []

        # Street (0173-1#02-AAO128#002)
        contact_elements.append({
            "modelType": "MultiLanguageProperty",
            "kind": "Instance",
            "semanticId": {
                "keys": [{
                    "type": "GlobalReference",
                    "value": "0173-1#02-AAO128#002"
                }],
                "type": "ModelReference"
            },
            "idShort": "Street",
            "value": [
                {
                    "language": "en",
                    "text": address_data.get("street", "Unknown Street")
                }
            ]
        })

        # Zipcode (0173-1#02-AAO129#002)
        contact_elements.append({
            "modelType": "Property",
            "kind": "Instance",
            "semanticId": {
                "keys": [{
                    "type": "GlobalReference",
                    "value": "0173-1#02-AAO129#002"
                }],
                "type": "ModelReference"
            },
            "idShort": "Zipcode",
            "valueType": "xs:string",
            "value": address_data.get("zipcode", "00000")
        })

        # CityTown (0173-1#02-AAO132#002)
        contact_elements.append({
            "modelType": "MultiLanguageProperty",
            "kind": "Instance",
            "semanticId": {
                "keys": [{
                    "type": "GlobalReference",
                    "value": "0173-1#02-AAO132#002"
                }],
                "type": "ModelReference"
            },
            "idShort": "CityTown",
            "value": [
                {
                    "language": "en",
                    "text": address_data.get("city", "Unknown City")
                }
            ]
        })

        # Email (0173-1#02-AAQ836#005)
        contact_elements.append({
            "modelType": "Property",
            "kind": "Instance",
            "semanticId": {
                "keys": [{
                    "type": "GlobalReference",
                    "value": "0173-1#02-AAQ836#005"
                }],
                "type": "ModelReference"
            },
            "idShort": "Email",
            "valueType": "xs:string",
            "value": contact_email
        })

        # Phone (0173-1#02-AAQ834#005)
        contact_elements.append({
            "modelType": "Property",
            "kind": "Instance",
            "semanticId": {
                "keys": [{
                    "type": "GlobalReference",
                    "value": "0173-1#02-AAQ834#005"
                }],
                "type": "ModelReference"
            },
            "idShort": "Phone",
            "valueType": "xs:string",
            "value": contact_phone
        })

        # ContactInformation Collection
        elements.append({
            "modelType": "SubmodelElementCollection",
            "kind": "Instance",
            "semanticId": {
                "keys": [{
                    "type": "GlobalReference",
                    "value": "0173-1#02-AAQ837#005"
                }],
                "type": "ModelReference"
            },
            "idShort": "ContactInformation",
            "value": contact_elements
        })

        return elements