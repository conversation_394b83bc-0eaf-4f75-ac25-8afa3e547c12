# -*- coding: utf-8 -*-
from odoo import http
from odoo.http import request
import base64
import json
import pandas as pd
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import requests
import time
from datetime import datetime

import logging
_logger = logging.getLogger(__name__)

class ZemaAasViewerAssetOverview(http.Controller):

    @http.route('/server/AAS/<path:product_slug>/', type='http', website=True, auth='public', methods=['GET'], priority=10)
    def asset_aas(self, product_slug):
        """
        Handle custom product URLs like: /server/AAS/ur10e-ur10e-309/
        Extract product ID from the end of the slug
        """
        try:
            # Extract product ID from the end of the slug
            # Format: {default_code}-{product_name}-{product_id}
            parts = product_slug.rstrip('/').split('-')
            if not parts:
                return request.not_found()

            # The product ID should be the last part
            try:
                product_id = int(parts[-1])
            except (ValueError, IndexError):
                return request.not_found()

            # Get the product
            product = request.env['product.product'].sudo().browse(product_id)
            if not product.exists():
                return request.not_found()

            # Log debug information
            _logger.info(f"=== AAS Page Load Debug (PATH ROUTING) ===")
            _logger.info(f"Product slug: {product_slug}")
            _logger.info(f"Extracted product ID: {product_id}")
            _logger.info(f"Product name: {product.name}")
            _logger.info(f"Product code: {product.default_code}")
            _logger.info(f"THIS IS THE NEW PATH-BASED ROUTE!")

            return http.request.render('zema_aas_viewer.aas',{
                'product': product,
                'thumbnail': product.image_1920,
                'idShort': product.default_code,
                'submodel_nameplate_infos': self.get_submodel_nameplate_info(product),
                'submodel_technical_data_infos': self.get_submodel_technical_data_info(product),
                'submodel_documentation_infos': self.get_submodel_documentation_info(product),
                'submodel_costs_infos': self.get_submodel_costs_info(product),
                'submodel_meta_data_infos': self.get_submodel_meta_data_info(product),
                'submodel_capabilities_infos': self.get_submodel_capabilities_info(product),
                'submodel_bom_infos': json.dumps(self.get_submodel_bom_info(product)),
                'similar_products': self.get_similar_products(product)
            })

        except Exception as e:
            _logger.error(f"Error in asset_aas for slug {product_slug}: {str(e)}")
            return request.not_found()

    @http.route('/server/AAS/test/<path:product_slug>/', type='http', website=True, auth='public', methods=['GET'])
    def asset_aas_test(self, product_slug):
        """Test route to verify path-based routing works"""
        parts = product_slug.rstrip('/').split('-')
        try:
            product_id = int(parts[-1])
            product = request.env['product.product'].sudo().browse(product_id)
            return request.make_response(
                f"<h1>Test Route Working!</h1><p>Slug: {product_slug}</p><p>Extracted ID: {product_id}</p><p>Product: {product.name if product.exists() else 'Not found'}</p>",
                headers=[('Content-Type', 'text/html')]
            )
        except Exception as e:
            return request.make_response(f"<h1>Error:</h1><p>{str(e)}</p>", headers=[('Content-Type', 'text/html')])

    @http.route('/api/nameplate/<int:product_id>', type='http', auth='public', methods=['GET'])
    def api_nameplate(self, product_id):
        product = http.request.env['product.product'].sudo().browse(product_id)
        if not product.exists():
            return request.not_found()

        nameplate_data = self.get_submodel_nameplate_info(product)
        return request.make_response(
            json.dumps(nameplate_data),
            headers=[('Content-Type', 'application/json')]
        )

    @http.route(['/debug/product/<int:product_id>', '/server/AAS/<path:product_slug>/debug'], type='http', auth='public', methods=['GET'])
    def debug_product_info(self, product_id=None, product_slug=None):
        """Debug endpoint to check product ID resolution"""
        try:
            # Handle both routing methods
            if product_slug is not None:
                # Extract product ID from slug
                parts = product_slug.rstrip('/').split('-')
                if not parts:
                    return request.make_response("Invalid product slug", status=400)

                try:
                    extracted_id = int(parts[-1])
                except (ValueError, IndexError):
                    return request.make_response(f"Cannot extract product ID from slug: {product_slug}", status=400)

                product = request.env['product.product'].sudo().browse(extracted_id)
                resolved_via = "path routing"
                url_product_id = f"{extracted_id} (from slug: {product_slug})"
                product_id = extracted_id

            elif product_id is not None:
                product = request.env['product.product'].sudo().browse(product_id)
                resolved_via = "integer routing"
                url_product_id = product_id
            else:
                return request.make_response("No product specified", status=400)

            if not product.exists():
                return request.make_response(f"Product not found: {product_id}", status=404)

            debug_info = {
                "resolved_via": resolved_via,
                "url_product_id": url_product_id,
                "actual_product_id": product.id,
                "product_name": product.name,
                "product_code": product.default_code,
                "manufacturer": product.manufacturer.name if product.manufacturer else "No manufacturer",
                "current_url": request.httprequest.url,
                "referrer": request.httprequest.referrer,
                "generated_link": self.create_product_website_link(product),
                "slug_parsing_test": {
                    "input_slug": product_slug if product_slug else "N/A",
                    "extracted_parts": product_slug.rstrip('/').split('-') if product_slug else "N/A",
                    "last_part": product_slug.rstrip('/').split('-')[-1] if product_slug else "N/A"
                },
                "id_consistency_check": product_id == product.id,
                "timestamp": datetime.now().isoformat()
            }

            return request.make_response(
                json.dumps(debug_info, indent=2),
                headers=[('Content-Type', 'application/json')]
            )

        except Exception as e:
            return request.make_response(
                json.dumps({
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }),
                headers=[('Content-Type', 'application/json')],
                status=500
            )

 
    def get_submodel_nameplate_info(self,product):
        
 
        client_zip =''
        client_website=''
 
        
        if  product.manufacturer.zip: client_zip =  product.manufacturer.zip
        if  product.manufacturer.website: client_website =  product.manufacturer.website
        
        submodel_nameplate_infos =  [
                {
                'sme_type' : "[MLP]",
                'idShort' : "ManufacturerName",
                'identification' : "0173-1#02-AAO677#002",
                'value' : product.manufacturer.name
                },
                {
                'sme_type' : "[MLP]",
                'idShort' : "ManufacturerProductDesignation",
                'identification' : "0173-1#02-AAW338#001",
                'value' : product.name
                },
                {
                'sme_type' : "[SEC]",
                'idShort' : "ContactInformation",
                'identification' : "[IRDI] 0173-1#02-AAQ837#005",
                'value' : str(product.manufacturer.name) + ' | ' + client_zip +' '+ client_website
                },
                {
                'sme_type' : "[MLP]",
                'idShort' : "ManufacturerProductFamily",
                'identification' : "0173-1#02-AAU731#001",
                'value' : product.manufacturer_product_family
                },
                # {
                # 'sme_type' : "[MLP]",
                # 'idShort' : "ManufacturerProductType",
                # 'identification' : "0173-1#02-AAO057#002",
                # 'value' : ""
                # },
                {
                'sme_type' : "[MLP]",
                'idShort' : "ProductArticleNumberOfManufacturer",
                'identification' : "0173-1#02-AAO676#003",
                'value' : product.barcode
                },
                {
                'sme_type' : "[MLP]",
                'idShort' : "SerialNumber",
                'identification' : "0173-1#02-AAM556#002",
                'value' : product.default_code
                }
            ]
        return submodel_nameplate_infos
    
    def get_submodel_technical_data_info(self,product):
        submodel_technical_data_infos=[]
        attribute_indices =  product.combination_indices.split(",")
        if product.combination_indices: # if there are no attributes set to the product
            for indice in attribute_indices:
 
                product_template_attribute_value = http.request.env['product.template.attribute.value'].search([('id','=',int(indice))])
                idShort = product_template_attribute_value.attribute_id.name
                value = product_template_attribute_value.product_attribute_value_id.name
                submodel_technical_data_infos.append({'idShort':idShort,'value':value})
        else:pass
        
        return submodel_technical_data_infos
    
    def get_submodel_meta_data_info(self,product):
        submodel_meta_data_infos = []
        tags = product.tags_of_bom

        for tag in tags:
            submodel_meta_data_infos.append({
            'idShort' : tag.tags.tag_name,
            'category' : tag.tags.category_name.category_name
            }
            )
            
        return submodel_meta_data_infos
            
    def get_submodel_documentation_info(self,product):
        submodel_documentation_infos = []
        attachments = http.request.env['ir.attachment'].search([
                '|',
                '&', '&', ('res_model', '=', 'product.template'), ('res_id', '=', product.product_tmpl_id.id), ('product_downloadable', '=', True),
                '&', '&', ('res_model', '=', 'product.product'), ('res_id', '=', product.id), ('product_downloadable', '=', True)])

        for attachment in attachments:
            pdf_data = attachment.datas
            submodel_documentation_infos.append({
                'name':attachment.name,
                'attachment_id':attachment.id, 
                'attachment_rec':attachment,
                'date':attachment.create_date, 
                'uploaded_from':attachment.company_id.name,
                'content' :pdf_data})
        return submodel_documentation_infos
    
    def get_submodel_costs_info(self,product):
        submodel_costs_infos =  product.cost_table
        return submodel_costs_infos
    
    def get_submodel_capabilities_info(self,product):
        submodel_capabilities_infos = []

        tags = product.tags_of_bom
        for tag in tags:
            if tag.tags.category_name.category_name =="Capability":
                submodel_capabilities_infos.append({
                'sme_type' : "[MLP]",
                'idShort' : "Capability",
                'value' : tag.tags.tag_name
                }
                )
            else:pass
        return submodel_capabilities_infos
    
    def get_submodel_bom_info(self,product):
        submodel_bom_infos = self.build_bom_node_array(product)
        return submodel_bom_infos
    
    # Function to preprocess text (lowercase + stemming)
    def preprocess_text(text, stemmer):
        words = text.lower().split()
        stemmed_words = [stemmer.stem(word) for word in words]
        return " ".join(stemmed_words)

    def get_similar_products(self, product):
        similar_products=[]
        corpus = self.create_corpus_products()
        df = pd.DataFrame(corpus.items(), columns=['title', 'meta_data'])
        # Convert the text-based meta data into a matrix of TF-IDF features
        vectorizer = TfidfVectorizer()
        tfidf_matrix = vectorizer.fit_transform(df['meta_data'])   
        # Calculate the cosine similarity matrix
        cosine_sim = cosine_similarity(tfidf_matrix, tfidf_matrix)
        record_set = self.recommend(product.id, cosine_sim, df)
        recommended_products_list=[]
        for record in record_set:
            recommended_product = http.request.env['product.product'].search([('id','=',record)])
            recommended_products_list.append(recommended_product)
        
        return recommended_products_list
    
    def create_corpus_products(self):
        '''
        Loop over all product records to create the corpus in dict form {id:description}
        '''
        corpus = {}
        # stemmer = nltk.stem.snowball.GermanStemmer()
        for record in http.request.env['product.product'].search([]):
            item_as_doc=""
            
            if record.tags_of_bom:
                record_tags_of_bom = record.tags_of_bom
                for tag_record in record_tags_of_bom:
                    tag= tag_record.tags.tag_name
                    amount_of_tag_id_in_tags = tag_record.amount
                    item_as_doc = item_as_doc + (str(tag) + ' ')*amount_of_tag_id_in_tags
                
            if record.tags_on_level:
                record_tags_on_level = record.tags_on_level
                for tag_record_level in record_tags_on_level:
                    tag_level= tag_record_level.tag_name
                    item_as_doc = item_as_doc + (str(tag_level) + ' ')
            
            if record.name:
                item_as_doc = item_as_doc + str(record.name)  + " "
                
            if record.manufacturer.name:
                item_as_doc = item_as_doc + str(record.manufacturer.name)  + " "
                
            if record.product_type:
                item_as_doc = item_as_doc + str(record.product_type.product_type)+ " " + str(record.product_type.product_type)  + " "
                
            if record.type:
                item_as_doc = item_as_doc + " " + str(record.type)
                
            if  record.categ_id:
                item_as_doc = item_as_doc + " " + str(record.categ_id.name) + " " + str(record.categ_id.name) + " "

            if item_as_doc == "": 
                pass
            else: 
                # item_as_doc = self.preprocess_text(item_as_doc, stemmer)
                corpus.update({record.id : item_as_doc})
            
            
        _logger.info('--------------- CORPUS: {}'.format(json.dumps(corpus)))

        return corpus

    def recommend(self, title, cosine_sim, df):
        # Get the index of the movie that matches the title
        idx = df[df['title'] == title].index[0]

        # Get the pairwise similarity scores of all movies with that movie
        sim_scores = list(enumerate(cosine_sim[idx]))

        # Sort the movies based on the similarity scores
        sim_scores = sorted(sim_scores, key=lambda x: x[1], reverse=True)

        # Get the top 10 most similar movies
        sim_scores = sim_scores[1:11]

        # Get the movie indices
        movie_indices = [i[0] for i in sim_scores]

        # Return the top 10 most similar movies
        return df['title'].iloc[movie_indices].values.tolist()

    
    
    @http.route('/aas/<model("product.product"):product>/submodels/Documentation/<model("ir.attachment"):attachment>/download', website=True, auth='public')
    def download_files(self, attachment):
        '''
        Gets called on submodel/Documentation button click to download a file
        '''
        content_type = attachment.mimetype # returns i.e. "application/pdf"
        file_type = content_type.split("/")[1] #extract the file ending - here: "pdf"

        response = request.make_response(
            base64.b64decode(attachment.datas),
            headers=[
                ('Content-Type', content_type),
                ('Content-Disposition', 'attachment; filename="{}.{}"'.format(attachment.name, file_type))
            ]
        )
        return response
    
    def build_bom_node_array(self, product):
        '''
        loops recuriveliy over sale to get the complete BOM of Sale
        
        '''
        nodes = []
        nodes.append({
                "key":product.id, 
                "id": str(product.name),
                "defaultCode":product.default_code,
                "material_type":product.type,
                "category": product.categ_id.name,
                "name":product.name, 
                "price":product.lst_price, 
                "manufacturer_name":product.manufacturer.name,
                "link":self.create_product_website_link(product),
                "count":1,
                "tags":[]})

        self.loop_over_product(nodes,product)
        
        return nodes
    
    def loop_over_product(self, nodes, parent):
        product_bom = parent.lst_price_source_bom
        if product_bom:
            for element_line in product_bom.bom_line_ids:
                nodes.append({
                "key":element_line.product_id.id, 
                "id": str(element_line.product_id.name),
                "defaultCode":element_line.product_id.default_code,
                "material_type":element_line.product_id.type,
                "category":element_line.product_id.categ_id.name ,
                "name":element_line.product_id.name,
                "price":element_line.product_id.lst_price, 
                "manufacturer_name":element_line.product_id.manufacturer.name,
                "count":element_line.product_qty, 
                "link":self.create_product_website_link(element_line.product_id),
                "parent":parent.id,
                "tags":[]})

                if element_line.product_id.lst_price_source_bom: self.loop_over_product(nodes, element_line.product_id)
                else:pass

    def create_product_website_link(self,product):
        link="http://10.200.60.102:8069/server/AAS/"
        special_char_map = {ord('ä'):'a', ord('ü'):'u', ord('ö'):'o', ord('ß'):''}
        link = link + str(product.default_code).lower().replace(" ","-") + "-" + str(product.name).lower().replace(" ","-") + "-"+ str(product.id).lower().replace(" ","-") + "/"
        return link.translate(special_char_map)
    
    @http.route('/aas/<model("product.product"):pp>/submodels/BOM/download_json', type='http', website=True, auth="public")
    def download_json(self, pp):
        
        product = pp
        filename = product.default_code + ".json"
        
        nodes = self.build_bom_node_array(product)

        data = nodes
        
        response = request.make_response(
            json.dumps(data, indent=4),
            headers=[
                ('Content-Type', 'application/json'),
                ('Content-Disposition', 'attachment; filename="{filename}"')
            ]
        )
        return response

    @http.route(['/aas/<int:product_id>/download_aasx', '/server/AAS/<path:product_slug>/download_aasx'], type='http', auth='public', methods=['POST'], csrf=False)
    def download_aasx_automated(self, product_id=None, product_slug=None, **kwargs):
        """
        Automated AASX download endpoint that triggers Node-RED automation
        This endpoint is called when the Download AAS button is clicked
        Supports both integer product_id and path-based routing
        """
        try:
            # Handle both routing methods
            if product_slug is not None:
                # Path routing: /server/AAS/{slug}/download_aasx
                # Extract product ID from the end of the slug
                parts = product_slug.rstrip('/').split('-')
                if not parts:
                    return request.make_response(
                        json.dumps({
                            'status': 'error',
                            'message': f'Invalid product slug: {product_slug}'
                        }),
                        headers=[('Content-Type', 'application/json')]
                    )

                try:
                    product_id = int(parts[-1])
                except (ValueError, IndexError):
                    return request.make_response(
                        json.dumps({
                            'status': 'error',
                            'message': f'Cannot extract product ID from slug: {product_slug}'
                        }),
                        headers=[('Content-Type', 'application/json')]
                    )

                product = request.env['product.product'].sudo().browse(product_id)
                if not product.exists():
                    return request.make_response(
                        json.dumps({
                            'status': 'error',
                            'message': f'Product with ID {product_id} not found (from slug: {product_slug})'
                        }),
                        headers=[('Content-Type', 'application/json')]
                    )

            elif product_id is not None:
                # Integer routing: /aas/<int:product_id>/download_aasx
                product = request.env['product.product'].sudo().browse(product_id)
                if not product.exists():
                    return request.make_response(
                        json.dumps({
                            'status': 'error',
                            'message': f'Product with ID {product_id} not found'
                        }),
                        headers=[('Content-Type', 'application/json')]
                    )
            else:
                return request.make_response(
                    json.dumps({
                        'status': 'error',
                        'message': 'No product specified'
                    }),
                    headers=[('Content-Type', 'application/json')]
                )

            # Log the product details for debugging
            _logger.info(f"=== AAS Download Debug Info ===")
            _logger.info(f"Product ID from URL: {product_id}")
            _logger.info(f"Product Name: {product.name}")
            _logger.info(f"Product Default Code: {product.default_code}")
            _logger.info(f"Product Database ID: {product.id}")
            _logger.info(f"Using URL Product ID: {product_id} (not database ID: {product.id})")
            _logger.info(f"URL that led here: {request.httprequest.url}")
            _logger.info(f"Referrer: {request.httprequest.referrer}")

            # Node-RED automation endpoint (will be running on VM 10.200.60.107)
            nodered_url = "http://10.200.60.107:1880/api/aas/create-and-download"

            # Prepare payload for Node-RED with correct product_id
            payload = {
                'product_id': product_id,  # Use the URL parameter, not the database ID
                'product_code': product.default_code or f'PROD_{product_id}',
                'product_name': product.name or 'Unknown Product',
                'erp_api_url': f'http://10.200.60.102:8069/api/sai/aas/template',  # Use the correct JSON-RPC endpoint
                'timestamp': datetime.now().isoformat()
            }

            _logger.info(f"Triggering Node-RED automation for product {product.id}: {product.name}")
            _logger.info(f"Payload being sent to Node-RED: {json.dumps(payload, indent=2)}")

            # Call Node-RED automation endpoint
            response = requests.post(
                nodered_url,
                json=payload,
                timeout=30,  # 30 second timeout
                headers={'Content-Type': 'application/json'}
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('status') == 'success':
                    # Return success with download URL
                    return request.make_response(
                        json.dumps({
                            'status': 'success',
                            'message': f'AAS created successfully for {product.name}',
                            'download_url': result.get('download_url'),
                            'filename': result.get('filename', f'{product.default_code}_AAS.aasx')
                        }),
                        headers=[('Content-Type', 'application/json')]
                    )
                else:
                    # Node-RED returned error
                    return request.make_response(
                        json.dumps({
                            'status': 'error',
                            'message': result.get('message', 'Node-RED automation failed')
                        }),
                        headers=[('Content-Type', 'application/json')]
                    )
            else:
                # HTTP error from Node-RED
                _logger.error(f"Node-RED automation failed with status {response.status_code}: {response.text}")
                return request.make_response(
                    json.dumps({
                        'status': 'error',
                        'message': f'Automation service unavailable (Status: {response.status_code}). Please ensure Node-RED is running on VM 10.200.60.107.'
                    }),
                    headers=[('Content-Type', 'application/json')]
                )

        except requests.exceptions.Timeout:
            _logger.error(f"Timeout calling Node-RED automation for product {product_id}")
            return request.make_response(
                json.dumps({
                    'status': 'error',
                    'message': 'Automation service timeout. The AAS creation is taking longer than expected.'
                }),
                headers=[('Content-Type', 'application/json')]
            )

        except requests.exceptions.ConnectionError:
            _logger.error(f"Connection error calling Node-RED automation for product {product_id}")
            return request.make_response(
                json.dumps({
                    'status': 'error',
                    'message': 'Cannot connect to automation service. Please ensure Node-RED is running on VM 10.200.60.107.'
                }),
                headers=[('Content-Type', 'application/json')]
            )

        except Exception as e:
            _logger.error(f"Error in automated AASX download for product {product_id}: {str(e)}")
            return request.make_response(
                json.dumps({
                    'status': 'error',
                    'message': f'Internal error: {str(e)}'
                }),
                headers=[('Content-Type', 'application/json')]
            )