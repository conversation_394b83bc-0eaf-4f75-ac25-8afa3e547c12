# Docker
.env
docker-compose.override.yml

# Logs
*.log
logs/

# Node-RED runtime files (auto-generated)
nodered/node_modules/
nodered/package-lock.json
nodered/.npm/
nodered/.node-red/

# BaSyx data directories
basyx-server/Basyx_KoKIRo_10.3.1.52_ZeMA/mosquitto/data/
basyx-server/Basyx_KoKIRo_10.3.1.52_ZeMA/mosquitto/log/

# Temporary files
*.tmp
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.iml

# Backup files
*.backup
*.bak

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/

# Java
*.class
*.jar
*.war
*.ear
target/
build/

# Database
*.db
*.sqlite
*.sqlite3

# AASX files (Asset Administration Shell packages)
*.aasx