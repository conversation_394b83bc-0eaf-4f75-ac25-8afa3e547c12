services:
  aas-env:
    image: eclipsebasyx/aas-environment:2.0.0-SNAPSHOT
    container_name: aas-env
    environment:
      - SERVER_PORT=8081
    volumes:
      - ./aas:/application/aas
      - ./basyx/aas-env.properties:/application/application.properties
    ports:
      - '8081:8081'
    restart: always
    depends_on:
      aas-registry:
        condition: service_healthy
      sm-registry:
        condition: service_healthy
      mongo:
        condition: service_healthy
      mosquitto:
        condition: service_healthy
  aas-registry:
    image: eclipsebasyx/aas-registry-log-mongodb:2.0.0-SNAPSHOT
    container_name: aas-registry
    ports:
      - '8082:8080'
    environment:
      - SERVER_PORT=8080
    volumes:
      - ./basyx/aas-registry.yml:/workspace/config/application.yml
    restart: always
    depends_on:
      mongo:
        condition: service_healthy
  sm-registry:
    image: eclipsebasyx/submodel-registry-log-mongodb:2.0.0-SNAPSHOT
    container_name: sm-registry
    ports:
      - '8083:8080'
    environment:
      - SERVER_PORT=8080
    volumes:
      - ./basyx/sm-registry.yml:/workspace/config/application.yml
    restart: always
    depends_on:
      mongo:
        condition: service_healthy
  aas-discovery:
    image: eclipsebasyx/aas-discovery:2.0.0-SNAPSHOT
    container_name: aas-discovery
    environment:
      - SERVER_PORT=8081
    volumes:
      - ./basyx/aas-discovery.properties:/application/application.properties
    ports:
      - '8084:8081'
    restart: always
    depends_on:
      mongo:
        condition: service_healthy
  mongo:
    image: mongo:5.0.10
    container_name: mongo
    environment:
      MONGO_INITDB_ROOT_USERNAME: mongoAdmin
      MONGO_INITDB_ROOT_PASSWORD: mongoPassword
    restart: always
    healthcheck:
      test: mongo
      interval: 10s
      timeout: 5s
      retries: 5
  mosquitto:
    image: eclipse-mosquitto:2.0.15
    container_name: mosquitto
    ports:
      - '1883:1883'
    volumes:
      - ./mosquitto:/mosquitto
    restart: always
    healthcheck:
      test:
        - CMD-SHELL
        - mosquitto_sub -p 1883 -t 'topic' -C 1 -E -i probe -W 3
      interval: 5s
      timeout: 10s
      start_period: 1s
      retries: 3
  aas-web-ui:
    image: eclipsebasyx/aas-gui:v2-240801
    container_name: aas-ui
    ports:
      - '3000:3000'
    environment:
      AAS_REGISTRY_PATH: http://*************:8082/shell-descriptors
      SUBMODEL_REGISTRY_PATH: http://*************:8083/submodel-descriptors
      AAS_REPO_PATH: http://*************:8081/shells
      SUBMODEL_REPO_PATH: http://*************:8081/submodels
      CD_REPO_PATH: http://*************:8081/concept-descriptions
      AAS_DISCOVERY_PATH: http://*************:8084/lookup/shells
      PRIMARY_COLOR: '#179C7D'
      LOGO_PATH: Logo/ZeMA.png
      DASHBOARD_SERVICE_PATH: http://*************:8085/api/elements
    restart: always
    depends_on:
      aas-env:
        condition: service_healthy
    volumes:
      - ./logo:/usr/src/app/dist/Logo
  dashboard-api:
    image: aaronzi/basyx-dashboard-api:SNAPSHOT_02
    container_name: dashboard-api
    ports:
      - '8085:8085'
    volumes:
      - ./basyx/aas-dashboard.yml:/application.yml
    restart: always
    depends_on:
      mongo:
        condition: service_healthy
