server.port=8081
basyx.backend=MongoDB
basyx.environment=file:aas
basyx.cors.allowed-origins=*
basyx.cors.allowed-methods=GET,POST,PATCH,DELETE,PUT,OPTIONS,HEAD
basyx.aasrepository.feature.registryintegration=http://aas-registry:8080
basyx.submodelrepository.feature.registryintegration=http://sm-registry:8080
basyx.externalurl=http://*************:8081
spring.servlet.multipart.max-file-size=500MB
spring.servlet.multipart.max-request-size=500MB
spring.data.mongodb.host=mongo
spring.data.mongodb.database=aas-env
spring.data.mongodb.authentication-database=admin
spring.data.mongodb.username=mongoAdmin
spring.data.mongodb.password=mongoPassword
mqtt.clientId=AAS-Env-8081
mqtt.hostname=mosquitto
mqtt.port=1883
basyx.aasrepository.feature.mqtt.enabled=true
basyx.submodelrepository.feature.mqtt.enabled=true


