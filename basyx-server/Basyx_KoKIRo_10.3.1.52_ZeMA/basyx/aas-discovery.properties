server.port=8081
spring.application.name=AAS Discovery Service
basyx.aasdiscoveryservice.name=aas-discovery-service
basyx.backend=MongoDB
basyx.cors.allowed-origins=*
basyx.cors.allowed-methods=GET,POST,PATCH,DELETE,PUT,OPTIONS,HEAD
spring.data.mongodb.host=mongo
spring.data.mongodb.database=aas-discovery
spring.data.mongodb.authentication-database=admin
spring.data.mongodb.username=mongoAdmin
spring.data.mongodb.password=mongoPassword
