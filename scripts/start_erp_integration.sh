#!/bin/bash

# ERP Integration Workflow Startup Script
# Starts all services required for ERP to AAS automation

set -e

echo "🚀 Starting ERP Integration Workflow for AAS Automation"
echo "======================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker first."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null 2>&1; then
    print_error "Docker Compose is not available. Please install Docker Compose."
    exit 1
fi

print_status "Stopping any existing containers..."
docker compose -f docker-compose.zema.yml down > /dev/null 2>&1 || true

print_status "Building and starting all services..."
docker compose -f docker-compose.zema.yml up -d --build

echo ""
print_status "Waiting for services to start..."

# Wait for services to be healthy
services=("mongo" "aas-env" "erp-api" "nodered")
max_wait=120
wait_time=0

for service in "${services[@]}"; do
    print_status "Waiting for $service to be ready..."
    
    while [ $wait_time -lt $max_wait ]; do
        if docker compose -f docker-compose.zema.yml ps $service | grep -q "healthy\|running"; then
            print_success "$service is ready"
            break
        fi
        
        if [ $wait_time -eq $max_wait ]; then
            print_warning "$service is taking longer than expected to start"
            break
        fi
        
        sleep 5
        wait_time=$((wait_time + 5))
    done
    wait_time=0
done

echo ""
print_status "Testing service connectivity..."

# Test ERP API
if curl -s http://10.200.60.107:8090/health > /dev/null; then
    print_success "ERP API is accessible on port 8090"
else
    print_warning "ERP API may not be ready yet on port 8090"
fi

# Test BaSyx AAS Environment
if curl -s http://10.200.60.107:8081/actuator/health > /dev/null; then
    print_success "BaSyx AAS Environment is accessible on port 8081"
else
    print_warning "BaSyx AAS Environment may not be ready yet on port 8081"
fi

# Test Node-RED
if curl -s http://10.200.60.107:1880 > /dev/null; then
    print_success "Node-RED is accessible on port 1880"
else
    print_warning "Node-RED may not be ready yet on port 1880"
fi

echo ""
echo "🎉 ERP Integration Workflow Started Successfully!"
echo "================================================"
echo ""
echo "📋 Available Services:"
echo "  • ERP API Server:        http://10.200.60.107:8090"
echo "  • ERP API Documentation: http://10.200.60.107:8090/docs"
echo "  • Node-RED Editor:       http://10.200.60.107:1880"
echo "  • AAS Dashboard:         http://10.200.60.107:1880/ui"
echo "  • BaSyx AAS Environment: http://10.200.60.107:8081"
echo "  • BaSyx Web UI:          http://10.200.60.107:3000"
echo ""
echo "🔧 Quick Test Commands:"
echo "  • Test ERP API:          curl http://10.200.60.107:8090/health"
echo "  • Get UR10 Data:         curl http://10.200.60.107:8090/api/products/ur10/nameplate"
echo "  • List AAS:              curl http://10.200.60.107:8081/shells"
echo ""
echo "📖 Workflow Steps:"
echo "  1. Open AAS Dashboard:   http://10.200.60.107:1880/ui"
echo "  2. Click 'Fetch UR10 Data from ERP'"
echo "  3. Click 'Create UR10 AAS from ERP Data'"
echo "  4. Download generated AASX file"
echo ""
echo "📚 Documentation:"
echo "  • ERP Integration Guide: docs/erp-integration-guide.md"
echo "  • API Reference:         docs/api-reference.md"
echo ""
echo "🛑 To stop all services:"
echo "  docker compose -f docker-compose.zema.yml down"
echo ""

# Optional: Open browser automatically
if command -v xdg-open &> /dev/null; then
    read -p "Open AAS Dashboard in browser? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        xdg-open http://10.200.60.107:1880/ui
    fi
elif command -v open &> /dev/null; then
    read -p "Open AAS Dashboard in browser? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        open http://10.200.60.107:1880/ui
    fi
fi

print_success "ERP Integration Workflow is ready for use!"
