#!/usr/bin/env python3
"""
Template <PERSON><PERSON><PERSON> for BaSyx AAS Server
Fetches existing Digital Nameplate templates and analyzes their structure
"""

import requests
import json
from typing import Dict, List, Any, Optional
import sys

class BaSyxTemplateFetcher:
    """Fetches and analyzes AAS templates from BaSyx server"""
    
    def __init__(self, basyx_url: str = "http://10.200.60.107:8081"):
        self.basyx_url = basyx_url
        self.session = requests.Session()
        
    def fetch_all_submodels(self) -> List[Dict[str, Any]]:
        """Fetch all submodels from BaSyx server"""
        try:
            response = self.session.get(f"{self.basyx_url}/submodels")
            response.raise_for_status()
            data = response.json()

            # Handle BaSyx paging response format
            if isinstance(data, dict) and 'result' in data:
                return data['result']
            # Handle different response formats
            elif isinstance(data, list):
                # If it's a list of submodel IDs, fetch each submodel individually
                submodels = []
                for submodel_id in data:
                    if isinstance(submodel_id, str):
                        try:
                            # Fetch individual submodel
                            submodel_response = self.session.get(f"{self.basyx_url}/submodels/{submodel_id}")
                            if submodel_response.status_code == 200:
                                submodel_data = submodel_response.json()
                                if isinstance(submodel_data, dict) and 'result' in submodel_data:
                                    submodels.extend(submodel_data['result'])
                                else:
                                    submodels.append(submodel_data)
                        except Exception as e:
                            print(f"⚠️ Could not fetch submodel {submodel_id}: {e}")
                    else:
                        submodels.append(submodel_id)
                return submodels
            else:
                return data if isinstance(data, list) else [data]
        except requests.RequestException as e:
            print(f"❌ Error fetching submodels: {e}")
            return []
    
    def fetch_all_aas(self) -> List[Dict[str, Any]]:
        """Fetch all AAS from BaSyx server"""
        try:
            response = self.session.get(f"{self.basyx_url}/shells")
            response.raise_for_status()
            data = response.json()

            # Handle BaSyx paging response format
            if isinstance(data, dict) and 'result' in data:
                return data['result']
            # Handle different response formats
            elif isinstance(data, list):
                # If it's a list of AAS IDs, fetch each AAS individually
                aas_list = []
                for aas_id in data:
                    if isinstance(aas_id, str):
                        try:
                            # URL encode the AAS ID
                            from urllib.parse import quote
                            encoded_id = quote(aas_id, safe='')
                            aas_response = self.session.get(f"{self.basyx_url}/shells/{encoded_id}")
                            if aas_response.status_code == 200:
                                aas_data = aas_response.json()
                                if isinstance(aas_data, dict) and 'result' in aas_data:
                                    aas_list.extend(aas_data['result'])
                                else:
                                    aas_list.append(aas_data)
                        except Exception as e:
                            print(f"⚠️ Could not fetch AAS {aas_id}: {e}")
                    else:
                        aas_list.append(aas_id)
                return aas_list
            else:
                return data if isinstance(data, list) else [data]
        except requests.RequestException as e:
            print(f"❌ Error fetching AAS: {e}")
            return []

    def fetch_submodels_from_aas(self, aas_id: str) -> List[Dict[str, Any]]:
        """Fetch submodels from a specific AAS"""
        try:
            # URL encode the AAS ID
            from urllib.parse import quote
            encoded_id = quote(aas_id, safe='')
            response = self.session.get(f"{self.basyx_url}/shells/{encoded_id}/submodels")
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            print(f"❌ Error fetching submodels from AAS {aas_id}: {e}")
            return []
    
    def find_nameplate_templates(self, submodels: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Find Digital Nameplate templates in submodels"""
        nameplate_semantic_id = "https://admin-shell.io/zvei/nameplate/1/0/Nameplate"
        templates = []

        print(f"🔍 Searching through {len(submodels)} submodels...")

        for i, submodel in enumerate(submodels):
            print(f"   Submodel {i+1}: {submodel.get('idShort', 'Unknown')} (ID: {str(submodel.get('id', 'Unknown'))[:50]}...)")
            print(f"      Raw submodel data: {str(submodel)[:200]}...")

            # Check by semantic ID
            if (submodel.get('semanticId') and
                submodel['semanticId'].get('keys') and
                len(submodel['semanticId']['keys']) > 0 and
                submodel['semanticId']['keys'][0].get('value') == nameplate_semantic_id):
                print(f"   ✅ Found by semantic ID: {submodel.get('idShort')}")
                templates.append(submodel)

            # Also check by idShort for SampleDigitalNameplateAAS
            elif submodel.get('idShort') == 'SampleDigitalNameplateAAS':
                print(f"   ✅ Found SampleDigitalNameplateAAS by idShort")
                templates.append(submodel)

            # Check if it contains "nameplate" or "digital" in the name
            elif (submodel.get('idShort', '').lower().find('nameplate') != -1 or
                  submodel.get('idShort', '').lower().find('digital') != -1):
                print(f"   🔍 Potential nameplate template: {submodel.get('idShort')}")
                templates.append(submodel)

        return templates
    
    def analyze_template_structure(self, template: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze the structure of a Digital Nameplate template"""
        analysis = {
            "id": template.get('id', 'Unknown'),
            "idShort": template.get('idShort', 'Unknown'),
            "total_elements": 0,
            "element_types": {},
            "semantic_ids": [],
            "collections": [],
            "properties": [],
            "multi_language_properties": []
        }
        
        elements = template.get('submodelElements', [])
        analysis["total_elements"] = len(elements)
        
        def analyze_elements(elements_list: List[Dict[str, Any]], prefix: str = ""):
            for element in elements_list:
                model_type = element.get('modelType', 'Unknown')
                id_short = element.get('idShort', 'Unknown')
                
                # Count element types
                if model_type in analysis["element_types"]:
                    analysis["element_types"][model_type] += 1
                else:
                    analysis["element_types"][model_type] = 1
                
                # Extract semantic IDs
                if element.get('semanticId') and element['semanticId'].get('keys'):
                    semantic_id = element['semanticId']['keys'][0].get('value', 'Unknown')
                    analysis["semantic_ids"].append({
                        "idShort": f"{prefix}{id_short}",
                        "semanticId": semantic_id,
                        "modelType": model_type
                    })
                
                # Categorize elements
                if model_type == 'SubmodelElementCollection':
                    analysis["collections"].append({
                        "idShort": f"{prefix}{id_short}",
                        "elements_count": len(element.get('value', []))
                    })
                    # Recursively analyze collection elements
                    if element.get('value'):
                        analyze_elements(element['value'], f"{prefix}{id_short}.")
                elif model_type == 'Property':
                    analysis["properties"].append({
                        "idShort": f"{prefix}{id_short}",
                        "valueType": element.get('valueType', 'Unknown'),
                        "category": element.get('category', 'Unknown')
                    })
                elif model_type == 'MultiLanguageProperty':
                    analysis["multi_language_properties"].append({
                        "idShort": f"{prefix}{id_short}",
                        "category": element.get('category', 'Unknown'),
                        "languages": [v.get('language', 'Unknown') for v in element.get('value', [])]
                    })
        
        analyze_elements(elements)
        return analysis
    
    def print_template_analysis(self, analysis: Dict[str, Any]):
        """Print a formatted analysis of the template"""
        print(f"\n📋 Template Analysis: {analysis['idShort']}")
        print(f"   ID: {analysis['id']}")
        print(f"   Total Elements: {analysis['total_elements']}")
        
        print(f"\n📊 Element Types:")
        for element_type, count in analysis['element_types'].items():
            print(f"   - {element_type}: {count}")
        
        print(f"\n📁 Collections ({len(analysis['collections'])}):")
        for collection in analysis['collections']:
            print(f"   - {collection['idShort']} ({collection['elements_count']} elements)")
        
        print(f"\n🏷️  Properties ({len(analysis['properties'])}):")
        for prop in analysis['properties'][:10]:  # Show first 10
            print(f"   - {prop['idShort']} ({prop['valueType']}, {prop['category']})")
        if len(analysis['properties']) > 10:
            print(f"   ... and {len(analysis['properties']) - 10} more")
        
        print(f"\n🌐 Multi-Language Properties ({len(analysis['multi_language_properties'])}):")
        for mlp in analysis['multi_language_properties'][:10]:  # Show first 10
            langs = ', '.join(mlp['languages'])
            print(f"   - {mlp['idShort']} ({langs}, {mlp['category']})")
        if len(analysis['multi_language_properties']) > 10:
            print(f"   ... and {len(analysis['multi_language_properties']) - 10} more")
        
        print(f"\n🔗 Semantic IDs (first 15):")
        for semantic in analysis['semantic_ids'][:15]:
            print(f"   - {semantic['idShort']}: {semantic['semanticId']}")
        if len(analysis['semantic_ids']) > 15:
            print(f"   ... and {len(analysis['semantic_ids']) - 15} more")

def main():
    """Main function to fetch and analyze templates"""
    print("🔍 Fetching Digital Nameplate templates from BaSyx server...")

    fetcher = BaSyxTemplateFetcher()

    # Fetch all AAS first
    print("📥 Fetching all AAS...")
    aas_list = fetcher.fetch_all_aas()
    print(f"   Found {len(aas_list)} AAS")

    # Look for SampleDigitalNameplateAAS
    sample_aas = None
    for i, aas in enumerate(aas_list):
        print(f"   AAS {i+1}: {aas.get('idShort', 'Unknown')} (ID: {str(aas.get('id', 'Unknown'))[:50]}...)")
        print(f"      Raw AAS data: {str(aas)[:200]}...")
        if aas.get('idShort') == 'SampleDigitalNameplateAAS':
            sample_aas = aas
            print(f"   ✅ Found SampleDigitalNameplateAAS!")

    # If we found SampleDigitalNameplateAAS, fetch its submodels
    sample_submodels = []
    if sample_aas:
        print(f"\n📥 Fetching submodels from SampleDigitalNameplateAAS...")
        sample_submodels = fetcher.fetch_submodels_from_aas(sample_aas['id'])
        print(f"   Found {len(sample_submodels)} submodels in SampleDigitalNameplateAAS")

    # Fetch all submodels
    print("\n📥 Fetching all submodels...")
    submodels = fetcher.fetch_all_submodels()
    print(f"   Found {len(submodels)} submodels")

    # Combine all submodels
    all_submodels = submodels + sample_submodels

    # Find Digital Nameplate templates
    print("\n🔎 Looking for Digital Nameplate templates...")
    nameplate_templates = fetcher.find_nameplate_templates(all_submodels)
    print(f"   Found {len(nameplate_templates)} Digital Nameplate templates")
    
    if not nameplate_templates:
        print("❌ No Digital Nameplate templates found!")
        print("   This means we'll need to create the basic structure from scratch.")
        return
    
    # Analyze each template
    for i, template in enumerate(nameplate_templates):
        print(f"\n{'='*60}")
        print(f"Template {i+1}/{len(nameplate_templates)}")
        analysis = fetcher.analyze_template_structure(template)
        fetcher.print_template_analysis(analysis)
    
    # Save the first template for reference
    if nameplate_templates:
        with open('nameplate_template_sample.json', 'w') as f:
            json.dump(nameplate_templates[0], f, indent=2)
        print(f"\n💾 Saved first template to 'nameplate_template_sample.json'")
    
    print(f"\n✅ Analysis complete!")

if __name__ == "__main__":
    main()
