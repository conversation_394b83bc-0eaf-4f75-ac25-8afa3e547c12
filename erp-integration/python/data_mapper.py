#!/usr/bin/env python3
"""
ERP to AAS Data Mapping Utility
Maps ERP product data to AAS Digital Nameplate format with proper semantic IDs
"""

from typing import Dict, List, Any
from datetime import datetime

class ERPToAASMapper:
    """Maps ERP data to AAS Digital Nameplate submodel elements"""

    # IDTA Digital Nameplate Semantic IDs
    SEMANTIC_IDS = {
        "nameplate": "https://admin-shell.io/zvei/nameplate/1/0/Nameplate",
        "manufacturer_name": "0173-1#02-AAO677#002",
        "product_designation": "0173-1#02-AAW338#001",
        "product_family": "0173-1#02-AAU731#001",
        "serial_number": "0173-1#02-AAM556#002",
        "year_of_construction": "0173-1#02-AAP906#001",
        "address": "0173-1#02-AAQ832#005",
        "street": "0173-1#02-AAO128#002",
        "zipcode": "0173-1#02-AAO129#002",
        "city": "0173-1#02-AAO132#002",
        "state": "0173-1#02-AAO133#002",
        "country": "0173-1#02-AAO134#002",
        "email": "0173-1#02-AAQ836#005",
        "email_address": "0173-1#02-AAO198#002"
    }

    @staticmethod
    def create_multi_language_property(semantic_id: str, values: List[Dict[str, str]],
                                     id_short: str, category: str = "PARAMETER") -> Dict[str, Any]:
        """Create a MultiLanguageProperty submodel element"""
        return {
            "modelType": "MultiLanguageProperty",
            "semanticId": {
                "keys": [{"type": "GlobalReference", "value": semantic_id}],
                "type": "ExternalReference"
            },
            "value": values,
            "category": category,
            "idShort": id_short
        }

    @staticmethod
    def create_property(semantic_id: str, value: str, id_short: str,
                       value_type: str = "xs:string", category: str = "PARAMETER") -> Dict[str, Any]:
        """Create a Property submodel element"""
        return {
            "modelType": "Property",
            "semanticId": {
                "keys": [{"type": "GlobalReference", "value": semantic_id}],
                "type": "ExternalReference"
            },
            "value": value,
            "valueType": value_type,
            "category": category,
            "idShort": id_short
        }

    @staticmethod
    def create_submodel_element_collection(semantic_id: str, elements: List[Dict[str, Any]],
                                         id_short: str, category: str = "VARIABLE") -> Dict[str, Any]:
        """Create a SubmodelElementCollection"""
        return {
            "modelType": "SubmodelElementCollection",
            "semanticId": {
                "keys": [{"type": "GlobalReference", "value": semantic_id}],
                "type": "ExternalReference"
            },
            "category": category,
            "idShort": id_short,
            "value": elements
        }

    @classmethod
    def map_erp_to_nameplate_elements(cls, erp_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Map ERP data to Digital Nameplate submodel elements"""

        elements = []

        # Manufacturer Name
        if "manufacturerName" in erp_data:
            manufacturer_values = []
            if isinstance(erp_data["manufacturerName"], dict):
                for lang, text in erp_data["manufacturerName"].items():
                    manufacturer_values.append({"language": lang, "text": text})
            else:
                manufacturer_values = [{"language": "en", "text": str(erp_data["manufacturerName"])}]

            elements.append(cls.create_multi_language_property(
                cls.SEMANTIC_IDS["manufacturer_name"],
                manufacturer_values,
                "ManufacturerName"
            ))

        # Product Designation
        if "productDesignation" in erp_data:
            product_values = []
            if isinstance(erp_data["productDesignation"], dict):
                for lang, text in erp_data["productDesignation"].items():
                    product_values.append({"language": lang, "text": text})
            else:
                product_values = [
                    {"language": "en", "text": str(erp_data["productDesignation"])},
                    {"language": "de", "text": str(erp_data["productDesignation"])}
                ]

            elements.append(cls.create_multi_language_property(
                cls.SEMANTIC_IDS["product_designation"],
                product_values,
                "ManufacturerProductDesignation"
            ))

        # Product Family
        if "productFamily" in erp_data:
            family_values = [
                {"language": "en", "text": str(erp_data["productFamily"])},
                {"language": "de", "text": str(erp_data["productFamily"])}
            ]

            elements.append(cls.create_multi_language_property(
                cls.SEMANTIC_IDS["product_family"],
                family_values,
                "ManufacturerProductFamily"
            ))

        # Serial Number
        if "serialNumber" in erp_data:
            elements.append(cls.create_property(
                cls.SEMANTIC_IDS["serial_number"],
                str(erp_data["serialNumber"]),
                "SerialNumber"
            ))

        # Year of Construction
        if "yearOfConstruction" in erp_data:
            elements.append(cls.create_property(
                cls.SEMANTIC_IDS["year_of_construction"],
                str(erp_data["yearOfConstruction"]),
                "YearOfConstruction"
            ))

        # Address
        if "address" in erp_data and isinstance(erp_data["address"], dict):
            address_elements = []
            address_data = erp_data["address"]

            if "street" in address_data:
                address_elements.append(cls.create_multi_language_property(
                    cls.SEMANTIC_IDS["street"],
                    [{"language": "en", "text": str(address_data["street"])},
                     {"language": "de", "text": str(address_data["street"])}],
                    "Street"
                ))

            if "zipcode" in address_data:
                address_elements.append(cls.create_multi_language_property(
                    cls.SEMANTIC_IDS["zipcode"],
                    [{"language": "en", "text": str(address_data["zipcode"])},
                     {"language": "de", "text": str(address_data["zipcode"])}],
                    "Zipcode",
                    "VARIABLE"
                ))

            if "city" in address_data:
                address_elements.append(cls.create_multi_language_property(
                    cls.SEMANTIC_IDS["city"],
                    [{"language": "en", "text": str(address_data["city"])},
                     {"language": "de", "text": str(address_data["city"])}],
                    "CityTown",
                    "VARIABLE"
                ))

            if "state" in address_data:
                address_elements.append(cls.create_multi_language_property(
                    cls.SEMANTIC_IDS["state"],
                    [{"language": "en", "text": str(address_data["state"])},
                     {"language": "de", "text": str(address_data["state"])}],
                    "StateCounty",
                    "VARIABLE"
                ))

            if "country" in address_data:
                address_elements.append(cls.create_multi_language_property(
                    cls.SEMANTIC_IDS["country"],
                    [{"language": "en", "text": str(address_data["country"])},
                     {"language": "de", "text": str(address_data["country"])}],
                    "NationalCode",
                    "VARIABLE"
                ))

            if address_elements:
                elements.append(cls.create_submodel_element_collection(
                    cls.SEMANTIC_IDS["address"],
                    address_elements,
                    "Address"
                ))

        # Email
        if "email" in erp_data:
            email_value = erp_data["email"]
            if isinstance(email_value, dict) and "address" in email_value:
                email_value = email_value["address"]

            email_elements = [
                cls.create_property(
                    cls.SEMANTIC_IDS["email_address"],
                    str(email_value),
                    "EmailAddress",
                    "xs:string",
                    "VARIABLE"
                )
            ]

            elements.append(cls.create_submodel_element_collection(
                cls.SEMANTIC_IDS["email"],
                email_elements,
                "Email"
            ))

        return elements

    @classmethod
    def create_complete_submodel(cls, submodel_id: str, erp_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a complete Digital Nameplate submodel from ERP data"""

        elements = cls.map_erp_to_nameplate_elements(erp_data)

        return {
            "modelType": "Submodel",
            "kind": "Instance",
            "semanticId": {
                "keys": [{
                    "type": "Submodel",
                    "value": cls.SEMANTIC_IDS["nameplate"]
                }],
                "type": "ModelReference"
            },
            "id": submodel_id,
            "idShort": "DigitalNameplate",
            "submodelElements": elements
        }