#!/bin/bash

# Start ERP API Server
# This script starts the mock ERP API server for UR10 product data

echo "Starting Mock ERP API Server for UR10 Product Data..."

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "Error: Python3 is not installed"
    exit 1
fi

# Check if pip is installed
if ! command -v pip3 &> /dev/null; then
    echo "Error: pip3 is not installed"
    exit 1
fi

# Install dependencies if requirements.txt exists
if [ -f "requirements.txt" ]; then
    echo "Installing Python dependencies..."
    pip3 install -r requirements.txt
else
    echo "Warning: requirements.txt not found"
fi

# Start the server
echo "Starting ERP API server on http://*************:8090"
echo "API Documentation available at: http://*************:8090/docs"
echo "Health check: http://*************:8090/health"
echo "UR10 Nameplate data: http://*************:8090/api/products/ur10/nameplate"
echo ""
echo "Press Ctrl+C to stop the server"

python3 erp_api_server.py
