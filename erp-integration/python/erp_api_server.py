#!/usr/bin/env python3
"""
Mock ERP API Server for UR10 Robotic Arm Product Data
Provides Digital Nameplate information following IDTA standards
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, List, Optional, Any
import uvicorn
from datetime import datetime
import json

app = FastAPI(
    title="Mock ERP API Server",
    description="Provides UR10 robotic arm product data for AAS automation",
    version="1.0.0"
)

# Enable CORS for Node-RED integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Data Models
class MultiLanguageText(BaseModel):
    language: str
    text: str

class SemanticId(BaseModel):
    keys: List[Dict[str, str]]
    type: str

class SubmodelElement(BaseModel):
    modelType: str
    semanticId: SemanticId
    value: Any
    valueType: Optional[str] = None
    category: str
    idShort: str

class DigitalNameplateData(BaseModel):
    manufacturerName: List[MultiLanguageText]
    manufacturerProductDesignation: List[MultiLanguageText]
    manufacturerProductFamily: List[MultiLanguageText]
    serialNumber: str
    yearOfConstruction: str
    address: Dict[str, Any]
    email: Dict[str, str]
    phone: Dict[str, str]
    technicalSpecifications: Dict[str, Any]

# UR10 Product Database
UR10_PRODUCT_DATA = {
    "basic_info": {
        "product_id": "UR10-2024-001",
        "model": "UR10",
        "manufacturer": "Universal Robots",
        "product_family": "Collaborative Robot",
        "serial_number": "UR10240001",
        "year_of_construction": "2024",
        "manufacturing_date": "2024-01-15"
    },
    "technical_specifications": {
        "payload": "10 kg",
        "reach": "1300 mm",
        "weight": "33.5 kg",
        "repeatability": "±0.1 mm",
        "degrees_of_freedom": 6,
        "joint_ranges": {
            "base": "±360°",
            "shoulder": "±360°", 
            "elbow": "±360°",
            "wrist_1": "±360°",
            "wrist_2": "±360°",
            "wrist_3": "±360°"
        },
        "max_speed": {
            "joint": "180°/s",
            "tcp": "1 m/s"
        },
        "operating_temperature": "-10°C to +50°C",
        "protection_rating": "IP54",
        "power_consumption": "500W"
    },
    "company_info": {
        "manufacturer_name": {
            "en": "Universal Robots A/S",
            "de": "Universal Robots A/S"
        },
        "address": {
            "street": "Energivej 25",
            "zipcode": "66115",
            "city": "Odense S",
            "state": "Region of Southern Denmark",
            "country": "DK"
        },
        "contact": {
            "email": "<EMAIL>",
            
            "website": "https://www.ZeMA-robots.com"
        }
    },
    "certifications": [
        "CE Marking",
        "ISO 10218-1",
        "ISO 13849-1 (PLd)",
        "ANSI/RIA R15.06",
        "KCs"
    ]
}

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Mock ERP API Server for UR10 Robotic Arm",
        "version": "1.0.0",
        "endpoints": {
            "product_info": "/api/products/ur10",
            "nameplate": "/api/products/ur10/nameplate",
            "technical_specs": "/api/products/ur10/specifications",
            "aas_template": "/api/products/ur10/aas-template"
        }
    }

@app.get("/api/products/ur10")
async def get_ur10_product_info():
    """Get complete UR10 product information"""
    return {
        "status": "success",
        "product": UR10_PRODUCT_DATA,
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/products/ur10/nameplate")
async def get_ur10_nameplate():
    """Get UR10 Digital Nameplate data following IDTA standards"""
    
    nameplate_data = {
        "manufacturerName": [
            {"language": "en", "text": UR10_PRODUCT_DATA["company_info"]["manufacturer_name"]["en"]},
            {"language": "de", "text": UR10_PRODUCT_DATA["company_info"]["manufacturer_name"]["de"]}
        ],
        "manufacturerProductDesignation": [
            {"language": "en", "text": f"{UR10_PRODUCT_DATA['basic_info']['model']} Collaborative Robot"},
            {"language": "de", "text": f"{UR10_PRODUCT_DATA['basic_info']['model']} Kollaborativer Roboter"}
        ],
        "manufacturerProductFamily": [
            {"language": "en", "text": UR10_PRODUCT_DATA["basic_info"]["product_family"]},
            {"language": "de", "text": "Kollaborativer Roboter"}
        ],
        "serialNumber": UR10_PRODUCT_DATA["basic_info"]["serial_number"],
        "yearOfConstruction": UR10_PRODUCT_DATA["basic_info"]["year_of_construction"],
        "address": {
            "street": UR10_PRODUCT_DATA["company_info"]["address"]["street"],
            "zipcode": UR10_PRODUCT_DATA["company_info"]["address"]["zipcode"],
            "city": UR10_PRODUCT_DATA["company_info"]["address"]["city"],
            "state": UR10_PRODUCT_DATA["company_info"]["address"]["state"],
            "country": UR10_PRODUCT_DATA["company_info"]["address"]["country"]
        },
        "email": {
            "address": UR10_PRODUCT_DATA["company_info"]["contact"]["email"]
        },
        "technicalSpecifications": {
            "payload": UR10_PRODUCT_DATA["technical_specifications"]["payload"],
            "reach": UR10_PRODUCT_DATA["technical_specifications"]["reach"],
            "weight": UR10_PRODUCT_DATA["technical_specifications"]["weight"],
            "repeatability": UR10_PRODUCT_DATA["technical_specifications"]["repeatability"],
            "degrees_of_freedom": UR10_PRODUCT_DATA["technical_specifications"]["degrees_of_freedom"]
        }
    }
    
    return {
        "status": "success",
        "nameplate": nameplate_data,
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/products/ur10/specifications")
async def get_ur10_specifications():
    """Get detailed technical specifications"""
    return {
        "status": "success",
        "specifications": UR10_PRODUCT_DATA["technical_specifications"],
        "certifications": UR10_PRODUCT_DATA["certifications"],
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/products/ur10/aas-template")
async def get_ur10_aas_template():
    """Get AAS-ready template with proper semantic IDs following IDTA standards"""
    
    # Generate unique AAS ID
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    aas_id = f"https://universal-robots.com/aas/UR10_{UR10_PRODUCT_DATA['basic_info']['serial_number']}_{timestamp}"
    submodel_id = aas_id.replace('/aas/', '/sm/') + "/DigitalNameplate"
    
    aas_template = {
        "aas": {
            "modelType": "AssetAdministrationShell",
            "id": aas_id,
            "idShort": f"UR10_{UR10_PRODUCT_DATA['basic_info']['serial_number']}",
            "description": [
                {
                    "language": "en",
                    "text": f"Digital Nameplate AAS for UR10 Collaborative Robot {UR10_PRODUCT_DATA['basic_info']['serial_number']}"
                },
                {
                    "language": "de", 
                    "text": f"Digitales Typenschild AAS für UR10 Kollaborativer Roboter {UR10_PRODUCT_DATA['basic_info']['serial_number']}"
                }
            ],
            "administration": {
                "version": "1",
                "revision": "0"
            },
            "assetInformation": {
                "assetKind": "Instance",
                "assetType": "Instance",
                "globalAssetId": aas_id + "_asset"
            },
            "submodels": [
                {
                    "keys": [
                        {
                            "type": "Submodel",
                            "value": submodel_id
                        }
                    ],
                    "type": "ModelReference"
                }
            ]
        },
        "submodel": {
            "modelType": "Submodel",
            "kind": "Instance",
            "semanticId": {
                "keys": [{
                    "type": "Submodel",
                    "value": "https://admin-shell.io/zvei/nameplate/1/0/Nameplate"
                }],
                "type": "ModelReference"
            },
            "id": submodel_id,
            "idShort": "DigitalNameplate",
            "submodelElements": []  # Will be populated by mapping logic
        },
        "erp_data": {
            "manufacturerName": UR10_PRODUCT_DATA["company_info"]["manufacturer_name"],
            "productDesignation": f"{UR10_PRODUCT_DATA['basic_info']['model']} Collaborative Robot",
            "productFamily": UR10_PRODUCT_DATA["basic_info"]["product_family"],
            "serialNumber": UR10_PRODUCT_DATA["basic_info"]["serial_number"],
            "yearOfConstruction": UR10_PRODUCT_DATA["basic_info"]["year_of_construction"],
            "address": UR10_PRODUCT_DATA["company_info"]["address"],
            "email": UR10_PRODUCT_DATA["company_info"]["contact"]["email"],
            "phone": UR10_PRODUCT_DATA["company_info"]["contact"].get("phone", "No phone available"),
            "technicalSpecs": UR10_PRODUCT_DATA["technical_specifications"]
        }
    }
    
    return {
        "status": "success",
        "template": aas_template,
        "timestamp": datetime.now().isoformat()
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

if __name__ == "__main__":
    uvicorn.run(
        "erp_api_server:app",
        host="0.0.0.0",
        port=8090,
        reload=True,
        log_level="info"
    )
