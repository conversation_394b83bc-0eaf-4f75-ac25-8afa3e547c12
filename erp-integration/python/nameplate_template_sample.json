{"modelType": "Submodel", "kind": "Instance", "semanticId": {"keys": [{"type": "Submodel", "value": "https://admin-shell.io/zvei/nameplate/1/0/Nameplate"}], "type": "ModelReference"}, "id": "https://admin-shell.io/idta/Submodel/DigitalNameplate/1/0", "idShort": "DigitalNameplate", "submodelElements": [{"modelType": "MultiLanguageProperty", "semanticId": {"keys": [{"type": "GlobalReference", "value": "0173-1#02-AAO677#002"}], "type": "ExternalReference"}, "value": [{"language": "de", "text": "Muster AG"}], "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "One", "valueType": "xs:string"}], "category": "PARAMETER", "idShort": "ManufacturerName"}, {"modelType": "MultiLanguageProperty", "semanticId": {"keys": [{"type": "GlobalReference", "value": "0173-1#02-AAW338#001"}], "type": "ExternalReference"}, "value": [{"language": "de", "text": "ABC-123"}, {"language": "en", "text": "ABC-123"}], "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "One", "valueType": "xs:string"}], "category": "PARAMETER", "idShort": "ManufacturerProductDesignation"}, {"modelType": "MultiLanguageProperty", "semanticId": {"keys": [{"type": "GlobalReference", "value": "0173-1#02-AAU731#001"}], "type": "ExternalReference"}, "value": [{"language": "de", "text": "ABC"}], "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "One", "valueType": "xs:string"}], "category": "PARAMETER", "idShort": "ManufacturerProductFamily"}, {"modelType": "SubmodelElementCollection", "semanticId": {"keys": [{"type": "GlobalReference", "value": "0173-1#02-AAQ832#005"}], "type": "ExternalReference"}, "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "One", "valueType": "xs:string"}], "category": "VARIABLE", "idShort": "Address", "value": [{"modelType": "MultiLanguageProperty", "semanticId": {"keys": [{"type": "GlobalReference", "value": "0173-1#02-AAO127#003"}], "type": "ExternalReference"}, "value": [{"language": "de", "text": "<PERSON><PERSON><PERSON><PERSON>"}, {"language": "en", "text": "Sales"}], "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "ZeroToOne", "valueType": "xs:string"}], "category": "VARIABLE", "idShort": "Department"}, {"modelType": "MultiLanguageProperty", "semanticId": {"keys": [{"type": "GlobalReference", "value": "0173-1#02-AAO128#002"}], "type": "ExternalReference"}, "value": [{"language": "de", "text": "Musterstraße 1"}], "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "One", "valueType": "xs:string"}], "category": "PARAMETER", "idShort": "Street"}, {"modelType": "MultiLanguageProperty", "semanticId": {"keys": [{"type": "GlobalReference", "value": "0173-1#02-AAO129#002"}], "type": "ExternalReference"}, "value": [{"language": "de", "text": "12345"}], "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "One", "valueType": "xs:string"}], "category": "VARIABLE", "idShort": "Zipcode"}, {"modelType": "MultiLanguageProperty", "semanticId": {"keys": [{"type": "GlobalReference", "value": "0173-1#02-AAO130#002"}], "type": "ExternalReference"}, "value": [{"language": "de", "text": "PF 1234"}], "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "ZeroToOne", "valueType": "xs:string"}], "category": "VARIABLE", "idShort": "POBox"}, {"modelType": "MultiLanguageProperty", "semanticId": {"keys": [{"type": "GlobalReference", "value": "0173-1#02-AAO131#002"}], "type": "ExternalReference"}, "value": [{"language": "de", "text": "12345"}], "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "ZeroToOne", "valueType": "xs:string"}], "category": "VARIABLE", "idShort": "ZipCodeOfPOBox"}, {"modelType": "MultiLanguageProperty", "semanticId": {"keys": [{"type": "GlobalReference", "value": "0173-1#02-AAO132#002"}], "type": "ExternalReference"}, "value": [{"language": "de", "text": "Musterstadt"}], "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "One", "valueType": "xs:string"}], "category": "VARIABLE", "idShort": "CityTown"}, {"modelType": "MultiLanguageProperty", "semanticId": {"keys": [{"type": "GlobalReference", "value": "0173-1#02-AAO133#002"}], "type": "ExternalReference"}, "value": [{"language": "de", "text": "Muster-Bundesland"}], "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "ZeroToOne", "valueType": "xs:string"}], "category": "VARIABLE", "idShort": "StateCounty"}, {"modelType": "MultiLanguageProperty", "semanticId": {"keys": [{"type": "GlobalReference", "value": "0173-1#02-AAO134#002"}], "type": "ExternalReference"}, "value": [{"language": "de", "text": "DE"}], "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "One", "valueType": "xs:string"}], "category": "VARIABLE", "idShort": "NationalCode"}, {"modelType": "MultiLanguageProperty", "semanticId": {"keys": [{"type": "GlobalReference", "value": "0173-1#02-AAO135#002"}], "type": "ExternalReference"}, "value": [{"language": "de", "text": "DE123456789"}], "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "ZeroToOne", "valueType": "xs:string"}], "category": "VARIABLE", "idShort": "VATNumber"}, {"modelType": "MultiLanguageProperty", "semanticId": {"keys": [{"type": "GlobalReference", "value": "0173-1#02-AAO202#003"}], "type": "ExternalReference"}, "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "ZeroToOne", "valueType": "xs:string"}], "category": "VARIABLE", "idShort": "AddressRemarks"}, {"modelType": "Property", "semanticId": {"keys": [{"type": "GlobalReference", "value": "0173-1#02-AAQ326#002"}], "type": "ExternalReference"}, "valueType": "xs:string", "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "ZeroToOne", "valueType": "xs:string"}], "category": "VARIABLE", "idShort": "AddressOfAdditionalLink"}, {"modelType": "SubmodelElementCollection", "semanticId": {"keys": [{"type": "GlobalReference", "value": "0173-1#02-AAQ833#005"}], "type": "ExternalReference"}, "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "ZeroToMany", "valueType": "xs:string"}], "category": "VARIABLE", "idShort": "Phone", "value": [{"modelType": "MultiLanguageProperty", "semanticId": {"keys": [{"type": "GlobalReference", "value": "0173-1#02-AAO136#002"}], "type": "ExternalReference"}, "value": [{"language": "de", "text": "+491234567890"}], "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "One", "valueType": "xs:string"}], "category": "VARIABLE", "idShort": "TelephoneNumber"}, {"modelType": "Property", "semanticId": {"keys": [{"type": "GlobalReference", "value": "0173-1#02-AAO137#003"}], "type": "ExternalReference"}, "value": "1", "valueId": {"keys": [{"type": "GlobalReference", "value": "0173-1#07-AAS754#001"}], "type": "ExternalReference"}, "valueType": "xs:string", "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "ZeroToOne", "valueType": "xs:string"}], "category": "VARIABLE", "idShort": "TypeOfTelephone"}]}, {"modelType": "SubmodelElementCollection", "semanticId": {"keys": [{"type": "GlobalReference", "value": "0173-1#02-AAQ834#005"}], "type": "ExternalReference"}, "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "ZeroToMany", "valueType": "xs:string"}], "category": "VARIABLE", "idShort": "Fax", "value": [{"modelType": "MultiLanguageProperty", "semanticId": {"keys": [{"type": "GlobalReference", "value": "0173-1#02-AAO195#002"}], "type": "ExternalReference"}, "value": [{"language": "de", "text": "+491234567890"}], "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "One", "valueType": "xs:string"}], "category": "VARIABLE", "idShort": "FaxNumber"}, {"modelType": "Property", "semanticId": {"keys": [{"type": "GlobalReference", "value": "0173-1#02-AAO196#003"}], "type": "ExternalReference"}, "value": "1", "valueId": {"keys": [{"type": "GlobalReference", "value": "0173-1#07-AAS754#001"}], "type": "ExternalReference"}, "valueType": "xs:string", "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "ZeroToOne", "valueType": "xs:string"}], "category": "VARIABLE", "idShort": "TypeOfFaxNumber"}]}, {"modelType": "SubmodelElementCollection", "semanticId": {"keys": [{"type": "GlobalReference", "value": "0173-1#02-AAQ836#005"}], "type": "ExternalReference"}, "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "ZeroToMany", "valueType": "xs:string"}], "category": "VARIABLE", "idShort": "Email", "value": [{"modelType": "Property", "semanticId": {"keys": [{"type": "GlobalReference", "value": "0173-1#02-AAO198#002"}], "type": "ExternalReference"}, "value": "<EMAIL>", "valueType": "xs:string", "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "One", "valueType": "xs:string"}], "category": "VARIABLE", "idShort": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"modelType": "Property", "semanticId": {"keys": [{"type": "GlobalReference", "value": "0173-1#02-AAO199#003"}], "type": "ExternalReference"}, "value": "1", "valueId": {"keys": [{"type": "GlobalReference", "value": "0173-1#07-AAS754#001"}], "type": "ExternalReference"}, "valueType": "xs:string", "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "ZeroToOne", "valueType": "xs:string"}], "category": "VARIABLE", "idShort": "TypeOfEmailAddress"}, {"modelType": "MultiLanguageProperty", "semanticId": {"keys": [{"type": "GlobalReference", "value": "0173-1#02-AAO200#002"}], "type": "ExternalReference"}, "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "ZeroToOne", "valueType": "xs:string"}], "category": "VARIABLE", "idShort": "PublicKey"}, {"modelType": "MultiLanguageProperty", "semanticId": {"keys": [{"type": "GlobalReference", "value": "0173-1#02-AAO201#002"}], "type": "ExternalReference"}, "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "ZeroToOne", "valueType": "xs:string"}], "category": "VARIABLE", "idShort": "TypeOfPublickKey"}]}]}, {"modelType": "SubmodelElementCollection", "semanticId": {"keys": [{"type": "GlobalReference", "value": "https://admin-shell.io/zvei/nameplate/1/0/Nameplate/Markings"}], "type": "ExternalReference"}, "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "ZeroToOne", "valueType": "xs:string"}], "category": "VARIABLE", "idShort": "Markings", "value": [{"modelType": "SubmodelElementCollection", "semanticId": {"keys": [{"type": "GlobalReference", "value": "https://admin-shell.io/zvei/nameplate/1/0/Nameplate/Markings/Marking"}], "type": "ExternalReference"}, "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "OneToMany", "valueType": "xs:string"}], "category": "VARIABLE", "idShort": "CE", "value": [{"modelType": "Property", "semanticId": {"keys": [{"type": "GlobalReference", "value": "https://admin-shell.io/zvei/nameplate/1/0/Nameplate/Markings/Marking/MarkingName"}], "type": "ExternalReference"}, "valueId": {"keys": [{"type": "GlobalReference", "value": "0173-1#07-DAA603#004"}], "type": "ExternalReference"}, "valueType": "xs:string", "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "One", "valueType": "xs:string"}], "category": "VARIABLE", "idShort": "<PERSON><PERSON><PERSON><PERSON>"}, {"modelType": "Property", "semanticId": {"keys": [{"type": "GlobalReference", "value": "https://admin-shell.io/zvei/nameplate/1/0/Nameplate/Markings/Marking/MarkingAdditionalText"}], "type": "ExternalReference"}, "valueType": "xs:string", "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "ZeroToMany", "valueType": "xs:string"}], "category": "VARIABLE", "idShort": "MarkingAdditionalText"}, {"modelType": "File", "contentType": "image/png", "value": "/aasx/files/european-union-ce-marking.png", "semanticId": {"keys": [{"type": "GlobalReference", "value": "https://admin-shell.io/zvei/nameplate/1/0/Nameplate/Markings/Marking/MarkingFile"}], "type": "ExternalReference"}, "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "One", "valueType": "xs:string"}], "category": "VARIABLE", "idShort": "MarkingFile"}]}, {"modelType": "SubmodelElementCollection", "semanticId": {"keys": [{"type": "GlobalReference", "value": "https://admin-shell.io/zvei/nameplate/1/0/Nameplate/Markings/Marking"}], "type": "ExternalReference"}, "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "OneToMany", "valueType": "xs:string"}], "category": "VARIABLE", "idShort": "GS", "value": [{"modelType": "Property", "semanticId": {"keys": [{"type": "GlobalReference", "value": "https://admin-shell.io/zvei/nameplate/1/0/Nameplate/Markings/Marking/MarkingName"}], "type": "ExternalReference"}, "valueId": {"keys": [{"type": "GlobalReference", "value": "0173-1#07-AAA374#003"}], "type": "ExternalReference"}, "valueType": "xs:string", "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "One", "valueType": "xs:string"}], "category": "VARIABLE", "idShort": "<PERSON><PERSON><PERSON><PERSON>"}, {"modelType": "Property", "semanticId": {"keys": [{"type": "GlobalReference", "value": "https://admin-shell.io/zvei/nameplate/1/0/Nameplate/Markings/Marking/MarkingAdditionalText"}], "type": "ExternalReference"}, "valueType": "xs:string", "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "ZeroToMany", "valueType": "xs:string"}], "category": "VARIABLE", "idShort": "MarkingAdditionalText"}, {"modelType": "File", "contentType": "image/png", "value": "/aasx/files/601px-GS.png", "semanticId": {"keys": [{"type": "GlobalReference", "value": "https://admin-shell.io/zvei/nameplate/1/0/Nameplate/Markings/Marking/MarkingFile"}], "type": "ExternalReference"}, "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "One", "valueType": "xs:string"}], "category": "VARIABLE", "idShort": "MarkingFile"}]}]}, {"modelType": "SubmodelElementCollection", "semanticId": {"keys": [{"type": "GlobalReference", "value": "https://admin-shell.io/zvei/nameplate/1/0/Nameplate/AssetSpecificProperties"}], "type": "ExternalReference"}, "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "ZeroToOne", "valueType": "xs:string"}], "category": "VARIABLE", "idShort": "AssetSpecificProperties", "value": [{"modelType": "SubmodelElementCollection", "semanticId": {"keys": [{"type": "GlobalReference", "value": "https://admin-shell.io/zvei/nameplate/1/0/Nameplate/AssetSpecificProperties/GuidelineSpecificProperties"}], "type": "ExternalReference"}, "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "OneToMany", "valueType": "xs:string"}], "category": "VARIABLE", "idShort": "GuidelineSpecificProperties__1__", "value": [{"modelType": "Property", "semanticId": {"keys": [{"type": "GlobalReference", "value": "0173-1#02-AAO856#002"}], "type": "ExternalReference"}, "value": "EU Directive 2014/68/EU", "valueType": "xs:string", "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "One", "valueType": "xs:string"}], "category": "VARIABLE", "idShort": "GuidelineForConformityDeclaration"}, {"modelType": "Property", "semanticId": {"keys": [{"type": "GlobalReference", "value": "0173-1#02-BAA447#006"}], "type": "ExternalReference"}, "value": "101325", "valueType": "xs:integer", "category": "VARIABLE", "idShort": "maxPressure"}]}]}, {"modelType": "Property", "semanticId": {"keys": [{"type": "GlobalReference", "value": "0173-1#02-AAM556#002"}], "type": "ExternalReference"}, "value": "12345678", "valueType": "xs:string", "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "ZeroToOne", "valueType": "xs:string"}], "category": "PARAMETER", "idShort": "SerialNumber"}, {"modelType": "Property", "semanticId": {"keys": [{"type": "GlobalReference", "value": "0173-1#02-AAP906#001"}], "type": "ExternalReference"}, "value": "2021", "valueType": "xs:string", "qualifiers": [{"kind": "ConceptQualifier", "type": "Multiplicity", "value": "One", "valueType": "xs:string"}], "category": "PARAMETER", "idShort": "YearOfConstruction"}]}