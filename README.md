# AAS Automation Framework

Industrial automation framework combining Eclipse BaSyx (Asset Administration Shell) with Node-RED for IoT data flow management.

## Quick Start

### Prerequisites

- Docker (20.10+)
- Docker Compose (1.29+)
- 4GB RAM, 2GB disk space

### Installation

1. Clone and start:
   ```bash
   git clone <your-repo-url>
   cd Thesis
   docker compose up -d
   ```

   **For Mac (M1/M2) users:**
   ```bash
   docker compose -f docker-compose.mac.yml up -d
   ```

   **For ZeMA environment with ERP Integration (recommended):**
   ```bash
   ./scripts/start_erp_integration.sh
   ```

   Or manually:
   ```bash
   docker compose -f docker-compose.zema.yml up -d
   ```

2. Verify services:
   ```bash
   docker ps
   ```

## Service Access

| Service | URL | Description |
|---------|-----|-------------|
| AAS Web UI | http://YOUR_IP:3000 | Asset Administration Shell Interface |
| Node-RED | http://YOUR_IP:1880 | IoT Flow Programming |
| AAS Environment | http://YOUR_IP:8081 | AAS Environment Server |
| AAS Registry | http://YOUR_IP:8082 | AAS Registry Service |
| Submodel Registry | http://YOUR_IP:8083 | Submodel Registry Service |
| AAS Discovery | http://YOUR_IP:8084 | AAS Discovery Service |
| Dashboard API | http://YOUR_IP:8085 | Dashboard API Service |
| MQTT Broker | YOUR_IP:1883 | Mosquitto MQTT Broker |

*Replace YOUR_IP with your actual server IP (e.g., ************* for Tailscale)*

## Architecture

### BaSyx Services
- **AAS Environment**: Core Asset Administration Shell runtime
- **Registries**: AAS and Submodel registries with MongoDB storage
- **Discovery**: AAS discovery service
- **MQTT**: Mosquitto broker for IoT communication

### Node-RED
- **Visual Programming**: Flow-based IoT programming
- **Persistence**: Flows stored in `./nodered/flows/`
- **MQTT Integration**: Connected to local broker
- **Custom Packages**: Includes adm-zip for AASX generation
- **ERP Integration**: Automated AAS creation from ERP data

### ERP Integration
- **Mock ERP API**: Python FastAPI server with UR10 product data
- **Data Mapping**: ERP to AAS transformation with IDTA semantic IDs
- **Template-Based**: Uses Digital Nameplate templates
- **Automated Workflow**: Complete ERP-to-AASX generation

## Access Points

- **Node-RED Editor**: http://*************:1880
- **AAS Dashboard**: http://*************:1880/ui
- **BaSyx Web UI**: http://*************:3000
- **AAS Environment API**: http://*************:8081
- **AAS Registry**: http://*************:8082
- **Submodel Registry**: http://*************:8083
- **AAS Discovery**: http://*************:8084
- **ERP API Server**: http://*************:8090
- **ERP API Documentation**: http://*************:8090/docs

## Project Structure
```
Thesis/
├── docker-compose.yml          # Main orchestration (Linux/Production)
├── docker-compose.mac.yml      # Mac M1/M2 compatible version
├── docker-compose.zema.yml     # ZeMA environment with tested fixes
├── docker-compose.prod.yml     # Production deployment
├── setup-zema.sh              # ZeMA environment setup script
├── basyx-server/              # BaSyx configurations
├── nodered/                   # Node-RED service
│   ├── flows/                 # Flows (version controlled)
│   └── .config/               # Container config
├── erp-integration/           # ERP Integration components
│   ├── python/                # Mock ERP API server
│   │   ├── erp_api_server.py  # FastAPI server with UR10 data
│   │   ├── data_mapper.py     # ERP to AAS mapping utilities
│   │   ├── requirements.txt   # Python dependencies
│   │   └── Dockerfile         # Container configuration
│   └── api/                   # API documentation
├── scripts/                   # Automation scripts
│   ├── start_erp_integration.sh # Complete workflow startup
│   ├── setup.sh               # Environment setup
│   └── backup.sh              # Backup utilities
└── docs/                      # Documentation
    ├── erp-integration-guide.md # ERP workflow documentation
    ├── api-reference.md       # API documentation
    └── troubleshooting.md     # Common issues and solutions
    └── troubleshooting/       # Troubleshooting guides
```

## Configuration

### Deployment on Different IPs

Update `docker-compose.yml` AAS Web UI environment variables:
```yaml
environment:
  AAS_REGISTRY_PATH: http://NEW_IP:8082/shell-descriptors
  SUBMODEL_REGISTRY_PATH: http://NEW_IP:8083/submodel-descriptors
  AAS_REPO_PATH: http://NEW_IP:8081/shells
  SUBMODEL_REPO_PATH: http://NEW_IP:8081/submodels
  CD_REPO_PATH: http://NEW_IP:8081/concept-descriptions
  AAS_DISCOVERY_PATH: http://NEW_IP:8084/lookup/shells
  DASHBOARD_SERVICE_PATH: http://NEW_IP:8085/api/elements
```

*Node-RED flows use container names and require no changes*

### Docker Networking

**Network Configuration:**
- **Network Name**: `thesis-network`
- **Driver**: Bridge (default Docker bridge driver)
- **Isolation**: Services communicate internally using container names
- **External Access**: Services exposed via port mapping to localhost

**Network Architecture:**
```yaml
networks:
  thesis-network:
    driver: bridge
```

All services join the `thesis-network` bridge network, enabling:
- Internal communication using service names (e.g., `mongo:27017`, `aas-env:8081`)
- Network isolation from other Docker projects
- Automatic DNS resolution between containers

**Platform Differences:**
- **Linux/Production**: Uses default bridge networking
- **Mac (M1/M2)**: Same bridge network with x86_64 emulation compatibility
- **Port Mapping**: All platforms map services to `localhost` (127.0.0.1)

### Node-RED ↔ BaSyx Integration

**Connection Architecture:**
Node-RED communicates with BaSyx services using HTTP REST API calls over the Docker bridge network.

**Key Integration Patterns:**

1. **AAS Management (Asset Administration Shell)**
   ```javascript
   // Create new AAS
   msg.url = 'http://aas-env:8081/shells';
   msg.method = 'POST';
   msg.payload = aasData;
   ```

2. **Submodel Operations**
   ```javascript
   // Create submodels
   msg.url = 'http://aas-env:8081/submodels';
   msg.method = 'POST';
   msg.payload = submodelData;
   ```

3. **Data Retrieval**
   ```javascript
   // Fetch AAS list
   msg.url = 'http://aas-env:8081/shells';
   msg.method = 'GET';
   ```

4. **AASX Export/Serialization**
   ```javascript
   // Download AASX packages
   msg.url = `http://aas-env:8081/serialization?aasIds=${encodedId}`;
   ```

**Connection Benefits:**
- **Container-to-Container**: Direct internal communication (no external network required)
- **Service Discovery**: Use container names instead of IP addresses
- **Data Flow**: Node-RED flows can create, modify, and export AAS data
- **Automation**: Automated AASX generation and management workflows

## Development

### Managing Services
```bash
# Start all (Linux/Production)
docker compose up -d

# Start all (Mac M1/M2)
docker compose -f docker-compose.mac.yml up -d

# Start all (ZeMA environment - recommended for troubleshooting)
./setup-zema.sh
# or manually: docker compose -f docker-compose.zema.yml up -d

# Stop all
docker compose down
# or for Mac: docker compose -f docker-compose.mac.yml down
# or for ZeMA: docker compose -f docker-compose.zema.yml down

# View logs
docker compose logs [service-name]
# or for ZeMA: docker compose -f docker-compose.zema.yml logs [service-name]

# Rebuild after changes
docker compose build [service-name]
```

### Node-RED Development
- Access flows at http://YOUR_IP:1880
- Dashboard UI at http://YOUR_IP:1880/ui
- Data persists in `./nodered/flows/`
- Install packages via web interface or rebuild container

### Node-RED ↔ BaSyx Integration Examples

**Example 1: Creating an AAS with Digital Nameplate**
The Node-RED flows include pre-built functionality to:
1. Create Asset Administration Shells (AAS)
2. Generate Digital Nameplate submodels with ZeMA company data
3. Export complete AASX packages

**Example 2: ERP Integration Workflow**
Demonstrates Industry 4.0 ERP-to-AAS automation:
1. **Fetch ERP Data**: Retrieve UR10 product data from mock ERP API
2. **Data Mapping**: Transform ERP data using IDTA semantic IDs
3. **AAS Creation**: Generate standardized Digital Nameplate AAS
4. **AASX Export**: Download complete package: `UR10_Robot_DigitalNameplate.aasx`

**Example 3: Automated Workflows**
- **Quick AAS Creation**: Single-click AAS generation with predefined templates
- **ERP Integration**: Automated ERP-to-AAS transformation
- **Batch Processing**: Process multiple assets using flow-based automation
- **File Management**: Automatic AASX download to local filesystem (`/thesis` mount)

**Example 3: Real-time Integration**
```javascript
// Node-RED Function Node example
// Connect to BaSyx AAS Environment
msg.url = 'http://aas-env:8081/shells';
msg.method = 'GET';
msg.headers = {'Content-Type': 'application/json'};
return msg;
```

### Adding Node-RED Packages
Edit `./nodered/Dockerfile` and rebuild:
```bash
docker compose -f docker-compose.mac.yml build nodered
docker compose -f docker-compose.mac.yml up -d
```

## Troubleshooting

**Services not starting:**
```bash
docker compose logs
docker compose down && docker compose up -d
```

**For persistent issues, use ZeMA environment:**
```bash
./setup-zema.sh
```

**Port conflicts:**
Ensure ports 1880, 1883, 3000, 8081-8085 are available

**MongoDB/AVX compatibility issues:**
The ZeMA configuration uses MongoDB 4.4 for compatibility with systems without AVX support. If you encounter MongoDB errors, use:
```bash
docker compose -f docker-compose.zema.yml up -d
```

**Node-RED missing dashboard elements:**
If Node-RED dashboard is missing UI elements:
- For ZeMA environment: Packages are pre-installed, rebuild if needed:
  ```bash
  docker compose -f docker-compose.zema.yml build nodered
  docker compose -f docker-compose.zema.yml up -d
  ```
- For other environments: Install packages manually:
  ```bash
  docker exec nodered npm install adm-zip@^0.5.10 node-red-dashboard@^3.6.5
  docker compose restart nodered
  ```

**Network issues:**
```bash
# Check network configuration
docker network ls | grep thesis
docker network inspect thesis_thesis-network

# Check service connectivity
docker compose exec nodered ping aas-env
```

**ECONNREFUSED errors:**
Verify container names in Node-RED flows use `aas-env:8081` format

**For comprehensive troubleshooting guide:**
See `docs/troubleshooting/2025-06-16.md` for detailed error resolution steps.

## Features

- **AAS Management**: Create and manage Asset Administration Shells via Node-RED flows
- **Digital Nameplate**: Automated generation with ZeMA company templates
- **ERP Integration**: Complete ERP-to-AAS workflow with UR10 robotic arm data
- **Data Mapping**: IDTA-compliant semantic ID mapping for standardized AAS creation
- **AASX Export**: Generate and download AASX packages from AAS data
- **Mock ERP API**: Python FastAPI server with realistic product data
- **Template-Based Creation**: Uses Digital Nameplate templates for consistent structure
- **MQTT Integration**: IoT device communication via Mosquitto broker
- **Visual Programming**: Node-RED flow-based automation for AAS workflows
- **Web Interface**: Browser-based AAS management (BaSyx Web UI)
- **Container Integration**: Seamless communication between Node-RED and BaSyx services
- **Industry 4.0 Compliance**: Demonstrates real-world ERP system integration

## Git Workflow

Standard commits for tracking changes to configurations and flows. Node-RED runtime files excluded via .gitignore.