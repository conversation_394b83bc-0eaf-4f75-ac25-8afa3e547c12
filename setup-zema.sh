#!/bin/bash

# ZeMA BaSyx Environment Setup Script
# This script sets up the complete BaSyx AAS environment with Node-RED
# using the ZeMA-specific configuration

echo "Starting ZeMA BaSyx Environment Setup..."

# Stop any existing containers
echo "Stopping existing containers..."
docker compose -f docker-compose.zema.yml down 2>/dev/null || true

# Start all services
echo "Starting all services..."
docker compose -f docker-compose.zema.yml up -d

# Wait for services to initialize
echo "Waiting for services to initialize..."
sleep 30

# Check service status
echo "Checking service status..."
docker compose -f docker-compose.zema.yml ps

# Node-RED packages are pre-installed in the custom image
echo "Node-RED with pre-installed packages is ready..."

# Final status check
echo "Final status check..."
docker compose -f docker-compose.zema.yml ps

# Get the current IP address
IP=$(hostname -I | awk '{print $1}')

echo ""
echo "ZeMA BaSyx Environment is ready!"
echo ""
echo "Service URLs:"
echo "   Node-RED Editor:    http://$IP:1880"
echo "   Node-RED Dashboard: http://$IP:1880/ui"
echo "   AAS Web UI:         http://$IP:3000"
echo "   AAS Environment:    http://$IP:8081"
echo "   AAS Registry:       http://$IP:8082"
echo "   Submodel Registry:  http://$IP:8083"
echo "   AAS Discovery:      http://$IP:8084"
echo "   Dashboard API:      http://$IP:8085"
echo ""
echo "To stop all services:"
echo "   docker compose -f docker-compose.zema.yml down"
echo ""
echo "To view logs:"
echo "   docker compose -f docker-compose.zema.yml logs -f [service_name]"
echo ""