FROM nodered/node-red:latest

# Switch to root user
USER root

# Install system packages for native module compilation
RUN apk add --no-cache \
    python3 \
    py3-pip \
    make \
    g++ \
    git

# Switch to node-red user and install npm packages
USER node-red

# Install required Node.js packages
RUN npm install \
    adm-zip@^0.5.10 \
    node-red-dashboard@^3.6.5

# Copy settings file (Node-RED will use this if no settings exist)
COPY --chown=node-red:root .config/settings.js /usr/src/node-red/settings.js

EXPOSE 1880
CMD ["node-red"]