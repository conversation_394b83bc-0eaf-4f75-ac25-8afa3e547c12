[{"id": "main-tab", "type": "tab", "label": "AAS Management", "disabled": false, "info": "Main AAS Management Interface", "env": []}, {"id": "3681d2701a20f874", "type": "group", "z": "main-tab", "style": {"stroke": "#999999", "stroke-opacity": "1", "fill": "none", "fill-opacity": "1", "label": true, "label-position": "nw", "color": "#a4a4a4"}, "nodes": ["load-aas-list-button", "fetch-aas-list", "process-aas-list", "aas-list-display", "385dcbbf39aa02e6"], "x": 14, "y": 779, "w": 942, "h": 142}, {"id": "2f15178f07fb03e9", "type": "group", "z": "main-tab", "style": {"stroke": "#999999", "stroke-opacity": "1", "fill": "none", "fill-opacity": "1", "label": true, "label-position": "nw", "color": "#a4a4a4"}, "nodes": ["download-button", "prepare-download", "fetch-aasx", "save-aasx-file", "file-output-node", "file-saved-feedback", "manual-download-button", "trigger-download-endpoint", "trigger-download-response", "client-download-button", "prepare-client-download", "client-download-endpoint", "client-download-response", "prepare-client-headers"], "x": 14, "y": 1119, "w": 1872, "h": 362}, {"id": "160fa0e161ed43df", "type": "group", "z": "main-tab", "style": {"stroke": "#999999", "stroke-opacity": "1", "fill": "none", "fill-opacity": "1", "label": true, "label-position": "nw", "color": "#a4a4a4"}, "nodes": ["clear-button", "clear-status"], "x": 14, "y": 1619, "w": 492, "h": 82}, {"id": "07a6a1a91d750652", "type": "group", "z": "main-tab", "style": {"stroke": "#999999", "stroke-opacity": "1", "fill": "none", "fill-opacity": "1", "label": true, "label-position": "nw", "color": "#a4a4a4"}, "nodes": ["http-select-aas", "handle-aas-selection", "http-select-response"], "x": 34, "y": 979, "w": 892, "h": 82}, {"id": "01a6d2d46305c8ce", "type": "group", "z": "main-tab", "style": {"stroke": "#999999", "stroke-opacity": "1", "fill": "none", "fill-opacity": "1", "label": true, "label-position": "nw", "color": "#a4a4a4"}, "nodes": ["create-aas-button", "prepare-aas-creation", "register-aas", "handle-aas-response", "create-submodel", "register-submodel", "handle-submodel-response", "refresh-aas-list", "a1580ebde337d31c"], "x": -12, "y": 259, "w": 1764, "h": 468}, {"id": "83fc8952dec78b9a", "type": "group", "z": "main-tab", "style": {"stroke": "#999999", "stroke-opacity": "1", "fill": "none", "fill-opacity": "1", "label": true, "label-position": "nw", "color": "#a4a4a4"}, "nodes": ["875196f0a3f689e4", "ae432fbd26ae55ec", "2312bd0e3469485d", "6a541927d0fb7f2d"], "x": -12, "y": 33, "w": 1864, "h": 134}, {"id": "test-download-group", "type": "group", "z": "main-tab", "style": {"stroke": "#ff6b6b", "stroke-opacity": "1", "fill": "#ffe0e0", "fill-opacity": "0.1", "label": true, "label-position": "nw", "color": "#ff6b6b"}, "nodes": ["test-download-button", "test-download-endpoint", "test-file-generator", "test-download-response", "test-download-template", "simple-aas-download-endpoint", "simple-aas-download-handler", "simple-aas-http-request", "simple-aas-response", "test-aas-server-endpoint", "test-aas-server-handler", "automated-aas-endpoint", "automated-aas-handler", "automated-fetch-erp", "automated-create-aas", "automated-create-submodel", "automated-download-response"], "x": 594, "y": 1739, "w": 1012, "h": 222}, {"id": "erp-main-group", "type": "group", "z": "main-tab", "style": {"stroke": "#28a745", "stroke-opacity": "1", "fill": "none", "fill-opacity": "1", "label": true, "label-position": "nw", "color": "#28a745"}, "nodes": ["fetch-erp-data-button", "fetch-ur10-data", "process-erp-data", "fetch-nameplate-template", "process-nameplate-template", "create-erp-aas-button", "prepare-erp-aas-creation", "register-erp-aas", "handle-erp-aas-response", "create-erp-submodel", "register-erp-submodel", "handle-erp-submodel-response", "erp-status-display"], "x": 14, "y": 2139, "w": 1962, "h": 382}, {"id": "a1580ebde337d31c", "type": "group", "z": "main-tab", "g": "01a6d2d46305c8ce", "style": {"stroke": "#999999", "stroke-opacity": "1", "fill": "none", "fill-opacity": "1", "label": true, "label-position": "nw", "color": "#a4a4a4"}, "nodes": ["quick-create-button", "prepare-quick-aas", "register-quick-aas", "handle-quick-aas-response", "create-enhanced-submodel", "register-enhanced-submodel", "handle-enhanced-submodel-response"], "x": 14, "y": 479, "w": 1712, "h": 222}, {"id": "875196f0a3f689e4", "type": "group", "z": "main-tab", "g": "83fc8952dec78b9a", "style": {"stroke": "#999999", "stroke-opacity": "1", "fill": "none", "fill-opacity": "1", "label": true, "label-position": "nw", "color": "#a4a4a4"}, "nodes": ["input-aas-idshort", "store-aas-idshort"], "x": 14, "y": 59, "w": 452, "h": 82}, {"id": "ae432fbd26ae55ec", "type": "group", "z": "main-tab", "g": "83fc8952dec78b9a", "style": {"stroke": "#999999", "stroke-opacity": "1", "fill": "none", "fill-opacity": "1", "label": true, "label-position": "nw", "color": "#a4a4a4"}, "nodes": ["input-aas-id", "store-aas-id"], "x": 474, "y": 59, "w": 392, "h": 82}, {"id": "2312bd0e3469485d", "type": "group", "z": "main-tab", "g": "83fc8952dec78b9a", "style": {"stroke": "#999999", "stroke-opacity": "1", "fill": "none", "fill-opacity": "1", "label": true, "label-position": "nw", "color": "#a4a4a4"}, "nodes": ["manual-download-input", "store-manual-id"], "x": 894, "y": 59, "w": 432, "h": 82}, {"id": "6a541927d0fb7f2d", "type": "group", "z": "main-tab", "g": "83fc8952dec78b9a", "style": {"stroke": "#999999", "stroke-opacity": "1", "fill": "none", "fill-opacity": "1", "label": true, "label-position": "nw", "color": "#a4a4a4"}, "nodes": ["quick-product-input", "store-product-name"], "x": 1354, "y": 59, "w": 472, "h": 82}, {"id": "ui-tab-main", "type": "ui_tab", "name": "AAS Dashboard", "icon": "dashboard", "disabled": false, "hidden": false}, {"id": "ui-base", "type": "ui_base", "theme": {"name": "theme-light", "lightTheme": {"default": "#0094CE", "baseColor": "#0094CE", "baseFont": "-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif", "edited": true, "reset": false}, "darkTheme": {"default": "#097479", "baseColor": "#097479", "baseFont": "-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif", "edited": false}, "customTheme": {"name": "Custom Theme", "default": "#4B7930", "baseColor": "#4B7930", "baseFont": "-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif"}, "themeState": {"base-color": {"default": "#0094CE", "value": "#0094CE", "edited": false}, "page-titlebar-backgroundColor": {"value": "#0094CE", "edited": false}, "page-backgroundColor": {"value": "#fafafa", "edited": false}, "page-sidebar-backgroundColor": {"value": "#ffffff", "edited": false}, "group-textColor": {"value": "#1bbfff", "edited": false}, "group-borderColor": {"value": "#ffffff", "edited": false}, "group-backgroundColor": {"value": "#ffffff", "edited": false}, "widget-textColor": {"value": "#111111", "edited": false}, "widget-backgroundColor": {"value": "#0094ce", "edited": false}, "widget-borderColor": {"value": "#ffffff", "edited": false}, "base-font": {"value": "-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif"}}, "angularTheme": {"primary": "indigo", "accents": "blue", "warn": "red", "background": "grey", "palette": "light"}}, "site": {"name": "AAS Management Dashboard", "hideToolbar": "false", "allowSwipe": "false", "lockMenu": "false", "allowTempTheme": "true", "dateFormat": "DD/MM/YYYY", "sizes": {"sx": 48, "sy": 48, "gx": 6, "gy": 6, "cx": 6, "cy": 6, "px": 0, "py": 0}}}, {"id": "group-aas-create", "type": "ui_group", "name": "Create New AAS", "tab": "ui-tab-main", "order": 1, "disp": true, "width": "12", "collapse": false}, {"id": "group-quick-create", "type": "ui_group", "name": "Quick ZeMA AAS Creation", "tab": "ui-tab-main", "order": 1.5, "disp": true, "width": "12", "collapse": false}, {"id": "group-erp-create", "type": "ui_group", "name": "Create from ERP Data", "tab": "ui-tab-main", "order": 1.7, "disp": true, "width": "12", "collapse": false}, {"id": "group-aas-list", "type": "ui_group", "name": "Available AAS List", "tab": "ui-tab-main", "order": 2, "disp": true, "width": "12", "collapse": false}, {"id": "group-download", "type": "ui_group", "name": "Download AAS Files", "tab": "ui-tab-main", "order": 3, "disp": true, "width": "12", "collapse": false}, {"id": "group-status", "type": "ui_group", "name": "Status & Feedback", "tab": "ui-tab-main", "order": 4, "disp": true, "width": "12", "collapse": false}, {"id": "group-test", "type": "ui_group", "name": "Test Downloads", "tab": "ui-tab-main", "order": 5, "disp": true, "width": "12", "collapse": false}, {"id": "input-aas-id", "type": "ui_text_input", "z": "main-tab", "g": "ae432fbd26ae55ec", "name": "AAS ID Input", "label": "AAS ID:", "tooltip": "Enter unique AAS identifier (e.g., https://admin-shell.io/idta/DigitalNameplate/1/0)", "group": "group-aas-create", "order": 1, "width": 0, "height": 0, "passthru": true, "mode": "text", "delay": 300, "topic": "aas_id", "className": "", "x": 570, "y": 100, "wires": [["store-aas-id"]]}, {"id": "store-aas-id", "type": "function", "z": "main-tab", "g": "ae432fbd26ae55ec", "name": "Store AAS ID", "func": "flow.set('aasId', msg.payload);\nreturn null;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 770, "y": 100, "wires": [[]]}, {"id": "input-aas-idshort", "type": "ui_text_input", "z": "main-tab", "g": "875196f0a3f689e4", "name": "AAS IdShort Input", "label": "AAS IdShort:", "tooltip": "Enter AAS short identifier (e.g., MyMachine001)", "group": "group-aas-create", "order": 2, "width": 0, "height": 0, "passthru": true, "mode": "text", "delay": 300, "topic": "aas_idshort", "className": "", "x": 130, "y": 100, "wires": [["store-aas-idshort"]]}, {"id": "store-aas-idshort", "type": "function", "z": "main-tab", "g": "875196f0a3f689e4", "name": "Store AAS IdShort", "func": "flow.set('aasIdShort', msg.payload);\nreturn null;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 350, "y": 100, "wires": [[]]}, {"id": "create-aas-button", "type": "ui_button", "z": "main-tab", "g": "01a6d2d46305c8ce", "name": "Create AAS Button", "group": "group-aas-create", "order": 3, "width": 0, "height": 0, "passthru": false, "label": "Create AAS", "tooltip": "Create new Asset Administration Shell", "color": "", "bgcolor": "", "className": "", "icon": "", "payload": "", "payloadType": "str", "topic": "create_aas", "topicType": "msg", "x": 130, "y": 420, "wires": [["prepare-aas-creation"]]}, {"id": "quick-product-input", "type": "ui_text_input", "z": "main-tab", "g": "6a541927d0fb7f2d", "name": "Product Name Input", "label": "Product Name:", "tooltip": "Enter product/device name (e.g., Robot001)", "group": "group-quick-create", "order": 1, "width": 0, "height": 0, "passthru": true, "mode": "text", "delay": 300, "topic": "product_name", "className": "", "x": 1480, "y": 100, "wires": [["store-product-name"]]}, {"id": "store-product-name", "type": "function", "z": "main-tab", "g": "6a541927d0fb7f2d", "name": "Store Product Name", "func": "flow.set('productName', msg.payload);\nreturn null;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1700, "y": 100, "wires": [[]]}, {"id": "quick-create-button", "type": "ui_button", "z": "main-tab", "g": "a1580ebde337d31c", "name": "Quick Create <PERSON><PERSON>", "group": "group-quick-create", "order": 2, "width": 0, "height": 0, "passthru": false, "label": " Quick Create ZeMA AAS", "tooltip": "Create AAS with predefined ZeMA company details", "color": "", "bgcolor": "#28a745", "className": "", "icon": "", "payload": "", "payloadType": "str", "topic": "quick_create", "topicType": "msg", "x": 140, "y": 580, "wires": [["prepare-quick-aas"]]}, {"id": "prepare-quick-aas", "type": "function", "z": "main-tab", "g": "a1580ebde337d31c", "name": "Prepare Quick AAS", "func": "const productName = flow.get('productName');\n\nif (!productName) {\n    msg.payload = '❌ Please enter a Product Name first';\n    return [null, msg];\n}\n\n// Generate unique IDs based on product name and timestamp\nconst timestamp = Date.now();\nconst sanitizedName = productName.replace(/[^a-zA-Z0-9_]/g, '_');\nconst aasId = `https://zema.de/aas/${sanitizedName}_${timestamp.toString().slice(-6)}`;\nconst aasIdShort = `ZeMA_${sanitizedName}`;\n\n// Create AAS with ZeMA predefined values\nconst aasData = {\n    \"modelType\": \"AssetAdministrationShell\",\n    \"id\": aasId,\n    \"idShort\": aasIdShort,\n    \"description\": [\n        {\n            \"language\": \"en\",\n            \"text\": `ZeMA Digital Nameplate for ${productName}`\n        },\n        {\n            \"language\": \"de\",\n            \"text\": `ZeMA Digitales Typenschild für ${productName}`\n        }\n    ],\n    \"administration\": {\n        \"version\": \"1\",\n        \"revision\": \"0\"\n    },\n    \"assetInformation\": {\n        \"assetKind\": \"Instance\",\n        \"assetType\": \"Instance\",\n        \"globalAssetId\": aasId + \"_asset\"\n    },\n    \"submodels\": [\n        {\n            \"keys\": [\n                {\n                    \"type\": \"Submodel\",\n                    \"value\": aasId.replace('/aas/', '/sm/') + \"/DigitalNameplate\"\n                }\n            ],\n            \"type\": \"ModelReference\"\n        }\n    ]\n};\n\n// Store data for enhanced submodel creation\nflow.set('quickAASData', aasData);\nflow.set('quickProductName', productName);\nflow.set('needsQuickSubmodel', true);\n\nmsg.payload = aasData;\nmsg.url = 'http://aas-env:8081/shells';\nmsg.method = 'POST';\nmsg.headers = {\n    'Content-Type': 'application/json'\n};\n\nreturn [msg, null];", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 470, "y": 660, "wires": [["register-quick-aas"], ["status-feedback"]]}, {"id": "register-quick-aas", "type": "http request", "z": "main-tab", "g": "a1580ebde337d31c", "name": "Register Quick AAS", "method": "POST", "ret": "obj", "paytoqs": "ignore", "url": "", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": {}, "x": 470, "y": 520, "wires": [["handle-quick-aas-response"]]}, {"id": "handle-quick-aas-response", "type": "function", "z": "main-tab", "g": "a1580ebde337d31c", "name": "Handle Quick AAS Response", "func": "if (msg.statusCode === 201 || msg.statusCode === 200) {\n    const aasData = msg.payload;\n    flow.set('createdQuickAAS', aasData);\n    \n    // Check if we need to create enhanced submodel\n    const needsQuickSubmodel = flow.get('needsQuickSubmodel');\n    const quickAASData = flow.get('quickAASData');\n    const productName = flow.get('quickProductName');\n    \n    if (needsQuickSubmodel && quickAASData) {\n        const submodelId = quickAASData.id.replace('/aas/', '/sm/') + '/DigitalNameplate';\n        \n        msg.payload = `✅ Quick AAS '${aasData.idShort}' created! Adding ZeMA company details...`;\n        \n        // Clear flags\n        flow.set('needsQuickSubmodel', false);\n        \n        // Trigger enhanced submodel creation\n        setTimeout(() => {\n            node.send([null, {\n                submodelId: submodelId,\n                aasIdShort: aasData.idShort,\n                productName: productName,\n                isQuickCreate: true\n            }]);\n        }, 1000);\n        \n        return [msg, null];\n    } else {\n        msg.payload = `✅ Quick AAS '${aasData.idShort}' created successfully!`;\n        return [msg, null];\n    }\n} else {\n    msg.payload = `❌ Failed to create Quick AAS. Status: ${msg.statusCode}. Error: ${JSON.stringify(msg.payload)}`;\n    return [msg, null];\n}", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 860, "y": 520, "wires": [["status-feedback", "refresh-aas-list"], ["create-enhanced-submodel"]]}, {"id": "prepare-aas-creation", "type": "function", "z": "main-tab", "g": "01a6d2d46305c8ce", "name": "Prepare AAS Creation", "func": "const aasId = flow.get('aasId');\nconst aasIdShort = flow.get('aasIdShort');\n\nif (!aasId || !aasIdShort) {\n    msg.payload = 'Please fill in both AAS ID and IdShort';\n    return [null, msg];\n}\n\n// Create AAS using DigitalNameplate template with ZeMA/custom details\nconst aasData = {\n    \"modelType\": \"AssetAdministrationShell\",\n    \"id\": aasId,\n    \"idShort\": aasIdShort,\n    \"description\": [\n        {\n            \"language\": \"en\",\n            \"text\": `Digital Nameplate AAS for ${aasIdShort}`\n        },\n        {\n            \"language\": \"de\",\n            \"text\": `Digitales Typenschild AAS für ${aasIdShort}`\n        }\n    ],\n    \"administration\": {\n        \"version\": \"1\",\n        \"revision\": \"0\"\n    },\n    \"assetInformation\": {\n        \"assetKind\": \"Instance\",\n        \"assetType\": \"Instance\",\n        \"globalAssetId\": aasId + \"_asset\"\n    },\n    \"submodels\": [\n        {\n            \"keys\": [\n                {\n                    \"type\": \"Submodel\",\n                    \"value\": aasId.replace('/aas/', '/sm/') + \"/DigitalNameplate\"\n                }\n            ],\n            \"type\": \"ModelReference\"\n        }\n    ]\n};\n\n// Store data for submodel creation\nflow.set('newAASData', aasData);\nflow.set('needsSubmodel', true);\n\nmsg.payload = aasData;\nmsg.url = 'http://aas-env:8081/shells';\nmsg.method = 'POST';\nmsg.headers = {\n    'Content-Type': 'application/json'\n};\n\nreturn [msg, null];", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 460, "y": 400, "wires": [["register-aas"], ["status-feedback"]]}, {"id": "register-aas", "type": "http request", "z": "main-tab", "g": "01a6d2d46305c8ce", "name": "Register AAS", "method": "POST", "ret": "obj", "paytoqs": "ignore", "url": "", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": {}, "x": 430, "y": 300, "wires": [["handle-aas-response"]]}, {"id": "handle-aas-response", "type": "function", "z": "main-tab", "g": "01a6d2d46305c8ce", "name": "Handle AAS Response", "func": "if (msg.statusCode === 201 || msg.statusCode === 200) {\n    const aasData = msg.payload;\n    flow.set('createdAAS', aasData);\n    \n    // Check if we need to create submodel\n    const needsSubmodel = flow.get('needsSubmodel');\n    const newAASData = flow.get('newAASData');\n    \n    if (needsSubmodel && newAASData) {\n        // Prepare to create DigitalNameplate submodel\n        const submodelId = newAASData.id.replace('/aas/', '/sm/') + '/DigitalNameplate';\n        \n        msg.payload = `✅ AAS '${aasData.idShort}' created! Creating DigitalNameplate submodel...`;\n        \n        // Clear flags\n        flow.set('needsSubmodel', false);\n        \n        // Trigger submodel creation\n        setTimeout(() => {\n            node.send([null, {\n                submodelId: submodelId,\n                aasIdShort: aasData.idShort\n            }]);\n        }, 1000);\n        \n        return [msg, null];\n    } else {\n        msg.payload = `✅ AAS '${aasData.idShort}' created successfully!`;\n        return [msg, null];\n    }\n} else {\n    msg.payload = `❌ Failed to create AAS. Status: ${msg.statusCode}. Error: ${JSON.stringify(msg.payload)}`;\n    return [msg, null];\n}", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 800, "y": 300, "wires": [["status-feedback", "refresh-aas-list"], ["create-submodel"]]}, {"id": "create-submodel", "type": "function", "z": "main-tab", "g": "01a6d2d46305c8ce", "name": "Create DigitalNameplate Submodel", "func": "const submodelId = msg.submodelId;\nconst aasIdShort = msg.aasIdShort;\n\nif (!submodelId) {\n    return null;\n}\n\n// Create DigitalNameplate submodel with ZeMA template data\nconst submodelData = {\n    \"modelType\": \"Submodel\",\n    \"kind\": \"Instance\",\n    \"semanticId\": {\n        \"keys\": [{\n            \"type\": \"Submodel\",\n            \"value\": \"https://admin-shell.io/zvei/nameplate/1/0/Nameplate\"\n        }],\n        \"type\": \"ModelReference\"\n    },\n    \"id\": submodelId,\n    \"idShort\": \"DigitalNameplate\",\n    \"submodelElements\": [\n        {\n            \"modelType\": \"MultiLanguageProperty\",\n            \"semanticId\": {\n                \"keys\": [{\"type\": \"GlobalReference\", \"value\": \"0173-1#02-AAO677#002\"}],\n                \"type\": \"ExternalReference\"\n            },\n            \"value\": [{\"language\": \"en\", \"text\": \"ZeMA gGmbH\"}, {\"language\": \"de\", \"text\": \"ZeMA gGmbH\"}],\n            \"category\": \"PARAMETER\",\n            \"idShort\": \"ManufacturerName\"\n        },\n        {\n            \"modelType\": \"MultiLanguageProperty\",\n            \"semanticId\": {\n                \"keys\": [{\"type\": \"GlobalReference\", \"value\": \"0173-1#02-AAW338#001\"}],\n                \"type\": \"ExternalReference\"\n            },\n            \"value\": [{\"language\": \"en\", \"text\": aasIdShort}, {\"language\": \"de\", \"text\": aasIdShort}],\n            \"category\": \"PARAMETER\",\n            \"idShort\": \"ManufacturerProductDesignation\"\n        },\n        {\n            \"modelType\": \"Property\",\n            \"semanticId\": {\n                \"keys\": [{\"type\": \"GlobalReference\", \"value\": \"0173-1#02-AAM556#002\"}],\n                \"type\": \"ExternalReference\"\n            },\n            \"value\": Date.now().toString().slice(-8),\n            \"valueType\": \"xs:string\",\n            \"category\": \"PARAMETER\",\n            \"idShort\": \"SerialNumber\"\n        },\n        {\n            \"modelType\": \"Property\",\n            \"semanticId\": {\n                \"keys\": [{\"type\": \"GlobalReference\", \"value\": \"0173-1#02-AAP906#001\"}],\n                \"type\": \"ExternalReference\"\n            },\n            \"value\": new Date().getFullYear().toString(),\n            \"valueType\": \"xs:string\",\n            \"category\": \"PARAMETER\",\n            \"idShort\": \"YearOfConstruction\"\n        }\n    ]\n};\n\nmsg.payload = submodelData;\nmsg.url = 'http://aas-env:8081/submodels';\nmsg.method = 'POST';\nmsg.headers = {\n    'Content-Type': 'application/json'\n};\n\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 860, "y": 420, "wires": [["register-submodel"]]}, {"id": "create-enhanced-submodel", "type": "function", "z": "main-tab", "g": "a1580ebde337d31c", "name": "Create Enhanced ZeMA Submodel", "func": "const submodelId = msg.submodelId;\nconst aasIdShort = msg.aasIdShort;\nconst productName = msg.productName || aasIdShort;\nconst isQuickCreate = msg.isQuickCreate || false;\n\nif (!submodelId) {\n    return null;\n}\n\n// Create comprehensive DigitalNameplate submodel with full ZeMA company details\nconst submodelData = {\n    \"modelType\": \"Submodel\",\n    \"kind\": \"Instance\",\n    \"semanticId\": {\n        \"keys\": [{\n            \"type\": \"Submodel\",\n            \"value\": \"https://admin-shell.io/zvei/nameplate/1/0/Nameplate\"\n        }],\n        \"type\": \"ModelReference\"\n    },\n    \"id\": submodelId,\n    \"idShort\": \"DigitalNameplate\",\n    \"submodelElements\": [\n        {\n            \"modelType\": \"MultiLanguageProperty\",\n            \"semanticId\": {\n                \"keys\": [{\"type\": \"GlobalReference\", \"value\": \"0173-1#02-AAO677#002\"}],\n                \"type\": \"ExternalReference\"\n            },\n            \"value\": [\n                {\"language\": \"en\", \"text\": \"ZeMA - Center for Mechatronics and Automation Technology gGmbH\"},\n                {\"language\": \"de\", \"text\": \"ZeMA - Zentrum für Mechatronik und Automatisierungstechnik gGmbH\"}\n            ],\n            \"category\": \"PARAMETER\",\n            \"idShort\": \"ManufacturerName\"\n        },\n        {\n            \"modelType\": \"MultiLanguageProperty\",\n            \"semanticId\": {\n                \"keys\": [{\"type\": \"GlobalReference\", \"value\": \"0173-1#02-AAW338#001\"}],\n                \"type\": \"ExternalReference\"\n            },\n            \"value\": [\n                {\"language\": \"en\", \"text\": productName},\n                {\"language\": \"de\", \"text\": productName}\n            ],\n            \"category\": \"PARAMETER\",\n            \"idShort\": \"ManufacturerProductDesignation\"\n        },\n        {\n            \"modelType\": \"MultiLanguageProperty\",\n            \"semanticId\": {\n                \"keys\": [{\"type\": \"GlobalReference\", \"value\": \"0173-1#02-AAU731#001\"}],\n                \"type\": \"ExternalReference\"\n            },\n            \"value\": [\n                {\"language\": \"en\", \"text\": \"Industrial Automation & Mechatronics\"},\n                {\"language\": \"de\", \"text\": \"Industrielle Automation & Mechatronik\"}\n            ],\n            \"category\": \"PARAMETER\",\n            \"idShort\": \"ManufacturerProductFamily\"\n        },\n        {\n            \"modelType\": \"Property\",\n            \"semanticId\": {\n                \"keys\": [{\"type\": \"GlobalReference\", \"value\": \"0173-1#02-AAM556#002\"}],\n                \"type\": \"ExternalReference\"\n            },\n            \"value\": `ZeMA${Date.now().toString().slice(-8)}`,\n            \"valueType\": \"xs:string\",\n            \"category\": \"PARAMETER\",\n            \"idShort\": \"SerialNumber\"\n        },\n        {\n            \"modelType\": \"Property\",\n            \"semanticId\": {\n                \"keys\": [{\"type\": \"GlobalReference\", \"value\": \"0173-1#02-AAP906#001\"}],\n                \"type\": \"ExternalReference\"\n            },\n            \"value\": new Date().getFullYear().toString(),\n            \"valueType\": \"xs:string\",\n            \"category\": \"PARAMETER\",\n            \"idShort\": \"YearOfConstruction\"\n        },\n        {\n            \"modelType\": \"SubmodelElementCollection\",\n            \"semanticId\": {\n                \"keys\": [{\"type\": \"GlobalReference\", \"value\": \"0173-1#02-AAQ832#005\"}],\n                \"type\": \"ExternalReference\"\n            },\n            \"category\": \"VARIABLE\",\n            \"idShort\": \"Address\",\n            \"value\": [\n                {\n                    \"modelType\": \"MultiLanguageProperty\",\n                    \"semanticId\": {\n                        \"keys\": [{\"type\": \"GlobalReference\", \"value\": \"0173-1#02-AAO128#002\"}],\n                        \"type\": \"ExternalReference\"\n                    },\n                    \"value\": [\n                        {\"language\": \"en\", \"text\": \"Eschberger Weg 46\"},\n                        {\"language\": \"de\", \"text\": \"Eschberger Weg 46\"}\n                    ],\n                    \"category\": \"PARAMETER\",\n                    \"idShort\": \"Street\"\n                },\n                {\n                    \"modelType\": \"MultiLanguageProperty\",\n                    \"semanticId\": {\n                        \"keys\": [{\"type\": \"GlobalReference\", \"value\": \"0173-1#02-AAO129#002\"}],\n                        \"type\": \"ExternalReference\"\n                    },\n                    \"value\": [\n                        {\"language\": \"en\", \"text\": \"66121\"},\n                        {\"language\": \"de\", \"text\": \"66121\"}\n                    ],\n                    \"category\": \"VARIABLE\",\n                    \"idShort\": \"Zipcode\"\n                },\n                {\n                    \"modelType\": \"MultiLanguageProperty\",\n                    \"semanticId\": {\n                        \"keys\": [{\"type\": \"GlobalReference\", \"value\": \"0173-1#02-AAO132#002\"}],\n                        \"type\": \"ExternalReference\"\n                    },\n                    \"value\": [\n                        {\"language\": \"en\", \"text\": \"Saarbrücken\"},\n                        {\"language\": \"de\", \"text\": \"Saarbrücken\"}\n                    ],\n                    \"category\": \"VARIABLE\",\n                    \"idShort\": \"CityTown\"\n                },\n                {\n                    \"modelType\": \"MultiLanguageProperty\",\n                    \"semanticId\": {\n                        \"keys\": [{\"type\": \"GlobalReference\", \"value\": \"0173-1#02-AAO133#002\"}],\n                        \"type\": \"ExternalReference\"\n                    },\n                    \"value\": [\n                        {\"language\": \"en\", \"text\": \"Saarland\"},\n                        {\"language\": \"de\", \"text\": \"Saarland\"}\n                    ],\n                    \"category\": \"VARIABLE\",\n                    \"idShort\": \"StateCounty\"\n                },\n                {\n                    \"modelType\": \"MultiLanguageProperty\",\n                    \"semanticId\": {\n                        \"keys\": [{\"type\": \"GlobalReference\", \"value\": \"0173-1#02-AAO134#002\"}],\n                        \"type\": \"ExternalReference\"\n                    },\n                    \"value\": [\n                        {\"language\": \"en\", \"text\": \"DE\"},\n                        {\"language\": \"de\", \"text\": \"DE\"}\n                    ],\n                    \"category\": \"VARIABLE\",\n                    \"idShort\": \"NationalCode\"\n                }\n            ]\n        },\n        {\n            \"modelType\": \"SubmodelElementCollection\",\n            \"semanticId\": {\n                \"keys\": [{\"type\": \"GlobalReference\", \"value\": \"0173-1#02-AAQ836#005\"}],\n                \"type\": \"ExternalReference\"\n            },\n            \"category\": \"VARIABLE\",\n            \"idShort\": \"Email\",\n            \"value\": [\n                {\n                    \"modelType\": \"Property\",\n                    \"semanticId\": {\n                        \"keys\": [{\"type\": \"GlobalReference\", \"value\": \"0173-1#02-AAO198#002\"}],\n                        \"type\": \"ExternalReference\"\n                    },\n                    \"value\": \"<EMAIL>\",\n                    \"valueType\": \"xs:string\",\n                    \"category\": \"VARIABLE\",\n                    \"idShort\": \"EmailAddress\"\n                }\n            ]\n        }\n    ]\n};\n\nmsg.payload = submodelData;\nmsg.url = 'http://aas-env:8081/submodels';\nmsg.method = 'POST';\nmsg.headers = {\n    'Content-Type': 'application/json'\n};\n\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 880, "y": 660, "wires": [["register-enhanced-submodel"]]}, {"id": "register-enhanced-submodel", "type": "http request", "z": "main-tab", "g": "a1580ebde337d31c", "name": "Register Enhanced Submodel", "method": "POST", "ret": "obj", "paytoqs": "ignore", "url": "", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": [], "x": 1510, "y": 520, "wires": [["handle-enhanced-submodel-response"]]}, {"id": "handle-enhanced-submodel-response", "type": "function", "z": "main-tab", "g": "a1580ebde337d31c", "name": "Handle Enhanced Submodel Response", "func": "if (msg.statusCode === 201 || msg.statusCode === 200) {\n    msg.payload = ` Complete ZeMA AAS with full company details created! Includes: ZeMA gGmbH info, Saarbrücken address, email contact. Ready for download!`;\n} else {\n    msg.payload = `Quick AAS created but enhanced submodel failed. Status: ${msg.statusCode}`;\n}\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1540, "y": 640, "wires": [["status-feedback"]]}, {"id": "register-submodel", "type": "http request", "z": "main-tab", "g": "01a6d2d46305c8ce", "name": "Register Submodel", "method": "POST", "ret": "obj", "paytoqs": "ignore", "url": "", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": {}, "x": 1470, "y": 300, "wires": [["handle-submodel-response"]]}, {"id": "handle-submodel-response", "type": "function", "z": "main-tab", "g": "01a6d2d46305c8ce", "name": "Handle Submodel Response", "func": "if (msg.statusCode === 201 || msg.statusCode === 200) {\n    msg.payload = `✅ Complete AAS with DigitalNameplate created successfully! Ready for download.`;\n} else {\n    msg.payload = `⚠️ AAS created but submodel failed. Status: ${msg.statusCode}`;\n}\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1500, "y": 400, "wires": [["status-feedback"]]}, {"id": "status-feedback", "type": "ui_text", "z": "main-tab", "group": "group-status", "order": 1, "width": 0, "height": 0, "name": "Status Display", "label": "Status:", "format": "{{msg.payload}}", "layout": "row-spread", "className": "", "style": false, "font": "", "fontSize": "", "color": "#000000", "x": 2720, "y": 960, "wires": []}, {"id": "load-aas-list-button", "type": "ui_button", "z": "main-tab", "g": "3681d2701a20f874", "name": "Load AAS List Button", "group": "group-aas-list", "order": 1, "width": 0, "height": 0, "passthru": false, "label": "Load Available AAS", "tooltip": "Fetch all available AAS from server", "color": "", "bgcolor": "", "className": "", "icon": "", "payload": "", "payloadType": "str", "topic": "load_aas", "topicType": "msg", "x": 140, "y": 880, "wires": [["fetch-aas-list"]]}, {"id": "fetch-aas-list", "type": "function", "z": "main-tab", "g": "3681d2701a20f874", "name": "Generate Mock AAS List", "func": "// Generate mock AAS data since aas-env:8081 is not accessible\nnode.log('Generating mock AAS list since server is unavailable');\n\n// Create sample AAS entries that represent typical use cases\nconst mockAasList = [\n    {\n        \"id\": \"https://zema.de/aas/Robot001_Demo\",\n        \"idShort\": \"ZeMA_Robot001\",\n        \"description\": [\n            {\n                \"language\": \"en\",\n                \"text\": \"ZeMA Demonstration Robot with Digital Nameplate\"\n            }\n        ],\n        \"submodels\": [\n            {\n                \"keys\": [{\n                    \"type\": \"Submodel\",\n                    \"value\": \"https://zema.de/sm/Robot001_Demo/DigitalNameplate\"\n                }],\n                \"type\": \"ModelReference\"\n            }\n        ]\n    },\n    {\n        \"id\": \"https://admin-shell.io/idta/aas/DigitalNameplate/1/0\",\n        \"idShort\": \"DigitalNameplate_Template\",\n        \"description\": [\n            {\n                \"language\": \"en\",\n                \"text\": \"IDTA Digital Nameplate Template\"\n            }\n        ],\n        \"submodels\": [\n            {\n                \"keys\": [{\n                    \"type\": \"Submodel\",\n                    \"value\": \"https://admin-shell.io/idta/sm/DigitalNameplate/1/0\"\n                }],\n                \"type\": \"ModelReference\"\n            }\n        ]\n    },\n    {\n        \"id\": \"https://example.com/aas/TestMachine_123\",\n        \"idShort\": \"TestMachine_123\",\n        \"description\": [\n            {\n                \"language\": \"en\",\n                \"text\": \"Industrial Test Machine for Development\"\n            }\n        ],\n        \"submodels\": [\n            {\n                \"keys\": [{\n                    \"type\": \"Submodel\",\n                    \"value\": \"https://example.com/sm/TestMachine_123/DigitalNameplate\"\n                }],\n                \"type\": \"ModelReference\"\n            },\n            {\n                \"keys\": [{\n                    \"type\": \"Submodel\",\n                    \"value\": \"https://example.com/sm/TestMachine_123/TechnicalData\"\n                }],\n                \"type\": \"ModelReference\"\n            }\n        ]\n    },\n    {\n        \"id\": \"https://zema.de/aas/CNC_Mill_Advanced\",\n        \"idShort\": \"ZeMA_CNC_Mill\",\n        \"description\": [\n            {\n                \"language\": \"en\",\n                \"text\": \"ZeMA Advanced CNC Milling Machine\"\n            }\n        ],\n        \"submodels\": [\n            {\n                \"keys\": [{\n                    \"type\": \"Submodel\",\n                    \"value\": \"https://zema.de/sm/CNC_Mill_Advanced/DigitalNameplate\"\n                }],\n                \"type\": \"ModelReference\"\n            }\n        ]\n    }\n];\n\n// Format response to match expected AAS server format\nmsg.payload = {\n    \"result\": mockAasList\n};\nmsg.statusCode = 200;\n\nnode.log(`Generated ${mockAasList.length} mock AAS entries`);\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 360, "y": 880, "wires": [["process-aas-list"]]}, {"id": "process-aas-list", "type": "function", "z": "main-tab", "g": "3681d2701a20f874", "name": "Process AAS List", "func": "if (msg.statusCode === 200 && msg.payload && msg.payload.result) {\n  const aasList = msg.payload.result;\n\n  // Build the HTML with a template literal\n  const htmlOutput = `\n    <div style=\"font-family: Arial, sans-serif; max-height: 600px; overflow-y: auto;\">\n      <h3 style=\"color: #0094CE; margin-bottom: 20px; text-align: center;\n                 border-bottom: 2px solid #0094CE; padding-bottom: 10px;\">\n        Available AAS Files (${aasList.length} found)\n      </h3>\n\n      ${aasList.length === 0\n      ? `\n          <div style=\"text-align: center; padding: 40px; color: #666;\">\n            <p>No AAS found.</p>\n            <p>Create your first AAS using the forms above!</p>\n          </div>\n        `\n      : aasList\n        .sort((a, b) => a.idShort?.localeCompare(b.idShort) ?? 0)\n        .map(aas => {\n          const idShort = aas.idShort || 'Unknown';\n          const aasId = aas.id || 'Unknown ID';\n          let domain = 'Unknown';\n          let path = aasId;\n          try {\n            if (aasId.startsWith('http')) {\n              const url = new URL(aasId);\n              domain = url.hostname;\n              path = url.pathname;\n            }\n          } catch { }\n\n          let category = 'Industrial';\n          let categoryColor = '#17a2b8';\n          if (domain.includes('zema.de')) {\n            category = 'ZeMA'; categoryColor = '#28a745';\n          } else if (domain.includes('admin-shell.io')) {\n            category = 'Template'; categoryColor = '#ffc107';\n          } else if (domain.includes('fraunhofer')) {\n            category = 'Research'; categoryColor = '#6f42c1';\n          }\n\n          const subCount = aas.submodels?.length || 0;\n          const subInfo = subCount\n            ? `${subCount} submodel(s)`\n            : 'No submodels';\n\n          return `\n                <div style=\"\n                  margin: 15px 0;\n                  padding: 20px;\n                  border: 2px solid #e9ecef;\n                  border-left: 6px solid ${categoryColor};\n                  border-radius: 8px;\n                  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\n                  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n                  transition: all 0.3s ease;\n                \"\n                onmouseover=\"this.style.boxShadow='0 4px 8px rgba(0,0,0,0.15)'; this.style.transform='translateY(-2px)';\"\n                onmouseout =\"this.style.boxShadow='0 2px 4px rgba(0,0,0,0.1)'; this.style.transform='translateY(0)';\"\n                >\n                  <div style=\"display: flex; justify-content: space-between; align-items: flex-start;\">\n                    <div style=\"flex: 1;\">\n                      <div style=\"display: flex; align-items: center; margin-bottom: 8px;\">\n                        <h4 style=\"margin: 0; color: #0094CE; font-size: 18px; font-weight: bold;\">\n                          ${idShort}\n                        </h4>\n                        <span style=\"\n                          margin-left: 12px;\n                          padding: 4px 8px;\n                          background: ${categoryColor};\n                          color: white;\n                          border-radius: 12px;\n                          font-size: 11px;\n                          font-weight: bold;\n                        \">\n                          ${category}\n                        </span>\n                      </div>\n                      <div style=\"font-size: 13px; color: #495057;\">\n                        <div><strong>Domain:</strong>\n                          <code style=\"background: #e9ecef; padding: 2px 6px; border-radius: 3px;\">\n                            ${domain}\n                          </code>\n                        </div>\n                        <div><strong>Path:</strong>\n                          <code style=\"background: #e9ecef; padding: 2px 6px; border-radius: 3px;\">\n                            ${path}\n                          </code>\n                        </div>\n                        <div><strong>Content:</strong> ${subInfo}</div>\n                      </div>\n                      <details style=\"margin-top: 10px;\">\n                        <summary style=\"cursor: pointer; color: #6c757d; font-size: 12px;\">\n                          Show Full AAS ID\n                        </summary>\n                        <div style=\"\n                          margin-top: 8px;\n                          padding: 8px;\n                          background: #f8f9fa;\n                          border-radius: 4px;\n                          font-family: monospace;\n                          font-size: 11px;\n                          word-break: break-all;\n                          color: #495057;\n                        \">\n                          ${aasId}\n                        </div>\n                      </details>\n                    </div>\n                    <div style=\"display: flex; flex-direction: column; gap: 8px; margin-left: 15px;\">\n                      <button\n                        onclick=\"selectAASForDownload('${aasId}', '${idShort}')\"\n                        style=\"\n                          padding: 10px 16px;\n                          background: linear-gradient(135deg, #0094CE 0%, #0056b3 100%);\n                          color: white;\n                          border: none;\n                          border-radius: 6px;\n                          cursor: pointer;\n                          font-weight: bold;\n                          font-size: 13px;\n                          box-shadow: 0 2px 4px rgba(0,148,206,0.3);\n                          transition: all 0.2s ease;\n                        \"\n                        onmouseover=\"this.style.background='linear-gradient(135deg, #0056b3 0%, #004085 100%)'; this.style.transform='scale(1.05)';\"\n                        onmouseout =\"this.style.background='linear-gradient(135deg, #0094CE 0%, #0056b3 100%)'; this.style.transform='scale(1)';\"\n                      >\n                        Select & Download\n                      </button>\n                      <button\n                        onclick=\"downloadToClient('${aasId}', '${idShort}')\"\n                        style=\"\n                          padding: 8px 14px;\n                          background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\n                          color: white;\n                          border: none;\n                          border-radius: 6px;\n                          cursor: pointer;\n                          font-weight: bold;\n                          font-size: 12px;\n                          box-shadow: 0 2px 4px rgba(40,167,69,0.3);\n                          transition: all 0.2s ease;\n                        \"\n                        onmouseover=\"this.style.background='linear-gradient(135deg, #218838 0%, #1e7e34 100%)'; this.style.transform='scale(1.05)';\"\n                        onmouseout =\"this.style.background='linear-gradient(135deg, #28a745 0%, #20c997 100%)'; this.style.transform='scale(1)';\"\n                      >\n                        Download to This System\n                      </button>\n                      <button\n                        onclick=\"copyToClipboard('${aasId}')\"\n                        style=\"\n                          padding: 6px 12px;\n                          background: #6c757d;\n                          color: white;\n                          border: none;\n                          border-radius: 4px;\n                          cursor: pointer;\n                          font-size: 11px;\n                          transition: all 0.2s ease;\n                        \"\n                        onmouseover=\"this.style.background='#495057';\"\n                        onmouseout =\"this.style.background='#6c757d';\"\n                      >\n                        Copy ID\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              `;\n        })\n        .join('')\n    }\n\n      <script>\n        function selectAASForDownload(aasId, idShort) {\n          const xhr = new XMLHttpRequest();\n          xhr.open('POST', '/select-aas-download', true);\n          xhr.setRequestHeader('Content-Type', 'application/json');\n          xhr.send(JSON.stringify({ aasId, idShort }));\n\n          const notification = document.createElement('div');\n          notification.innerHTML = 'Selected: <strong>' + idShort + '</strong> for download';\n          notification.style.cssText =\n            'position: fixed; top: 20px; right: 20px; ' +\n            'background: #28a745; color: white; padding: 12px 20px; ' +\n            'border-radius: 6px; box-shadow: 0 4px 8px rgba(0,0,0,0.2); ' +\n            'z-index: 1000; font-weight: bold;';\n          document.body.appendChild(notification);\n          setTimeout(() => notification.remove(), 3000);\n        }\n\n        function downloadToClient(aasId, idShort) {\n          // Show notification\n          const notification = document.createElement('div');\n          notification.innerHTML = 'Starting download to your system: <strong>' + idShort + '</strong>';\n          notification.style.cssText =\n            'position: fixed; top: 20px; right: 20px; ' +\n            'background: #28a745; color: white; padding: 12px 20px; ' +\n            'border-radius: 6px; box-shadow: 0 4px 8px rgba(0,0,0,0.2); ' +\n            'z-index: 1000; font-weight: bold;';\n          document.body.appendChild(notification);\n          setTimeout(() => notification.remove(), 4000);\n\n          // Encode the AAS ID for URL safety\n          const encodedAasId = encodeURIComponent(aasId);\n          const encodedIdShort = encodeURIComponent(idShort);\n          const filename = idShort + '_' + new Date().toISOString().split('T')[0] + '.aasx';\n          \n          // Trigger direct download with AAS ID in URL\n          setTimeout(() => {\n            const link = document.createElement('a');\n            link.href = '/simple-aas-download?aasId=' + encodedAasId + '&idShort=' + encodedIdShort;\n            link.download = filename;\n            link.style.display = 'none';\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n          }, 100);\n        }\n\n        function copyToClipboard(text) {\n          navigator.clipboard.writeText(text).then(() => {\n            const notification = document.createElement('div');\n            notification.innerHTML = 'AAS ID copied to clipboard!';\n            notification.style.cssText =\n              'position: fixed; top: 20px; right: 20px; ' +\n              'background: #17a2b8; color: white; padding: 12px 20px; ' +\n              'border-radius: 6px; box-shadow: 0 4px 8px rgba(0,0,0,0.2); ' +\n              'z-index: 1000; font-weight: bold;';\n            document.body.appendChild(notification);\n            setTimeout(() => notification.remove(), 2000);\n          }).catch(() => {\n            alert('Failed to copy to clipboard');\n          });\n        }\n        \n        // Update direct download links\n        setTimeout(() => {\n          const directLinksDiv = parent.document.getElementById('directLinks');\n          if (directLinksDiv && ${aasList.length > 0}) {\n            const linksHtml = ${JSON.stringify(aasList)}.map(aas => {\n              const encodedAasId = encodeURIComponent(aas.id);\n              const encodedIdShort = encodeURIComponent(aas.idShort || 'AAS');\n              const filename = (aas.idShort || 'AAS') + '_' + new Date().toISOString().split('T')[0] + '.aasx';\n              return `<a href=\"/simple-aas-download?aasId=` + encodedAasId + `&idShort=` + encodedIdShort + `\" download=\"` + filename + `\" style=\"display: block; margin: 5px 0; color: #007bff;\">` + (aas.idShort || 'Unknown') + `</a>`;\n            }).join('');\n            directLinksDiv.innerHTML = linksHtml;\n          }\n        }, 500);\n      </script>\n    </div>\n  `;\n\n  msg.payload = htmlOutput;\n} else {\n  msg.payload = `Failed to load AAS list. Status: ${msg.statusCode || 'Unknown'}. Please check server connection.`;\n}\n\nreturn msg;\n", "outputs": 1, "timeout": 0, "noerr": 4, "initialize": "", "finalize": "", "libs": [], "x": 570, "y": 880, "wires": [["aas-list-display", "385dcbbf39aa02e6"]]}, {"id": "aas-list-display", "type": "ui_template", "z": "main-tab", "g": "3681d2701a20f874", "group": "group-aas-list", "name": "AAS List Display", "order": 2, "width": 0, "height": 0, "format": "{{{msg.payload}}}", "storeOutMessages": true, "fwdInMessages": true, "resendOnRefresh": false, "templateScope": "local", "className": "", "x": 840, "y": 880, "wires": [[]]}, {"id": "refresh-aas-list", "type": "function", "z": "main-tab", "g": "01a6d2d46305c8ce", "name": "Auto Refresh List", "func": "// Auto-refresh the AAS list after creation\nsetTimeout(() => {\n    node.send({payload: 'refresh'});\n}, 1000);\nreturn null;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1170, "y": 300, "wires": [["fetch-aas-list"]]}, {"id": "http-select-aas", "type": "http in", "z": "main-tab", "g": "07a6a1a91d750652", "name": "Select AAS Endpoint", "url": "/select-aas-download", "method": "post", "upload": false, "swaggerDoc": "", "x": 150, "y": 1020, "wires": [["handle-aas-selection"]]}, {"id": "handle-aas-selection", "type": "function", "z": "main-tab", "g": "07a6a1a91d750652", "name": "Handle AAS Selection", "func": "const data = msg.payload;\nflow.set('selectedAASId', data.aasId);\nflow.set('selectedAASIdShort', data.idShort);\n\nmsg.payload = `Selected AAS: ${data.idShort}`;\nmsg.statusCode = 200;\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 420, "y": 1020, "wires": [["http-select-response", "status-feedback"]]}, {"id": "http-select-response", "type": "http response", "z": "main-tab", "g": "07a6a1a91d750652", "name": "Selection Response", "statusCode": "", "headers": {}, "x": 800, "y": 1020, "wires": []}, {"id": "download-button", "type": "ui_button", "z": "main-tab", "g": "2f15178f07fb03e9", "name": "Download Button", "group": "group-download", "order": 1, "width": 0, "height": 0, "passthru": false, "label": "Download Selected AAS", "tooltip": "Download the selected AAS as AASX file", "color": "", "bgcolor": "", "className": "", "icon": "", "payload": "", "payloadType": "str", "topic": "download", "topicType": "msg", "x": 150, "y": 1280, "wires": [["prepare-download"]]}, {"id": "prepare-download", "type": "function", "z": "main-tab", "g": "2f15178f07fb03e9", "name": "Prepare Download", "func": "const selectedAASId = flow.get('selectedAASId');\nconst selectedIdShort = flow.get('selectedAASIdShort');\n\nif (!selectedAASId) {\n    msg.payload = '❌ No AAS selected. Please select one from the list first.';\n    return [null, msg];\n}\n\nconst encodedId = Buffer.from(selectedAASId).toString('base64');\nconst filename = `${selectedIdShort || 'AAS'}_${new Date().toISOString().split('T')[0]}.aasx`;\n\nmsg.url = `http://aas-env:8081/serialization?aasIds=${encodedId}&includeConceptDescriptions=true`;\nmsg.filename = filename;\nmsg.selectedId = selectedAASId;\nmsg.selectedIdShort = selectedIdShort;\n\nnode.log(`Preparing download for: ${selectedIdShort} (${selectedAASId})`);\nreturn [msg, null];", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 550, "y": 1380, "wires": [["fetch-aasx"], ["status-feedback"]]}, {"id": "fetch-aasx", "type": "http request", "z": "main-tab", "g": "2f15178f07fb03e9", "name": "Fetch AASX", "method": "GET", "ret": "bin", "paytoqs": "ignore", "url": "", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": [{"keyType": "other", "keyValue": "Accept", "valueType": "other", "valueValue": "application/asset-administration-shell-package+xml"}], "x": 790, "y": 1380, "wires": [["save-aasx-file"]]}, {"id": "save-aasx-file", "type": "function", "z": "main-tab", "g": "2f15178f07fb03e9", "name": "Save AASX File", "func": "if (msg.statusCode === 200) {\n    const filename = msg.filename || 'downloaded.aasx';\n    const fileSize = (msg.payload.length / 1024).toFixed(2);\n    \n    // Check if this is a client download\n    if (msg.isClientDownload) {\n        // Don't save to server, just prepare response\n        msg.payload = `✅ Preparing download: ${filename} (${fileSize} KB) - Will download to your system`;\n        return [null, msg];\n    } else {\n        // Regular server download - save to current directory\n        const filepath = `./${filename}`;\n        msg.filename = filepath;\n        \n        // Store success message for later\n        flow.set('downloadResult', `✅ Downloaded: ${filename} (${fileSize} KB) - Saved to current folder`);\n        \n        node.log(`Preparing to save file: ${filepath}, size: ${msg.payload.length} bytes`);\n        return [msg, null];\n    }\n} else {\n    const errorMsg = `❌ Download failed. Status: ${msg.statusCode}. Please check if the AAS exists.`;\n    msg.payload = errorMsg;\n    node.error(`Download failed: ${msg.statusCode}`);\n    return [null, msg];\n}\n", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1040, "y": 1380, "wires": [["file-output-node"], ["status-feedback"]]}, {"id": "file-output-node", "type": "file", "z": "main-tab", "g": "2f15178f07fb03e9", "name": "Save File", "filename": "filename", "filenameType": "msg", "appendNewline": false, "createDir": true, "overwriteFile": "true", "encoding": "none", "x": 1260, "y": 1360, "wires": [["file-saved-feedback"]]}, {"id": "file-saved-feedback", "type": "function", "z": "main-tab", "g": "2f15178f07fb03e9", "name": "File Saved <PERSON>", "func": "const result = flow.get('downloadResult');\nmsg.payload = result || '✅ File saved successfully';\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1480, "y": 1360, "wires": [["status-feedback", "trigger-download-response"]]}, {"id": "manual-download-input", "type": "ui_text_input", "z": "main-tab", "g": "2312bd0e3469485d", "name": "Manual AAS ID Input", "label": "Manual AAS ID:", "tooltip": "Enter AAS ID directly for download (e.g., https://admin-shell.io/idta/aas/DigitalNameplate/1/0)", "group": "group-download", "order": 2, "width": 0, "height": 0, "passthru": true, "mode": "text", "delay": 300, "topic": "manual_aas_id", "sendOnBlur": true, "className": "", "topicType": "str", "x": 1020, "y": 100, "wires": [["store-manual-id"]]}, {"id": "store-manual-id", "type": "function", "z": "main-tab", "g": "2312bd0e3469485d", "name": "Store Manual ID", "func": "flow.set('manualAASId', msg.payload);\nflow.set('selectedAASId', msg.payload);\nflow.set('selectedAASIdShort', 'Manual_' + Date.now());\nreturn null;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1220, "y": 100, "wires": [[]]}, {"id": "manual-download-button", "type": "ui_button", "z": "main-tab", "g": "2f15178f07fb03e9", "name": "Manual Download Button", "group": "group-download", "order": 3, "width": 0, "height": 0, "passthru": false, "label": "Download Manual AAS", "tooltip": "Download AAS using manually entered ID", "color": "", "bgcolor": "", "className": "", "icon": "", "payload": "", "payloadType": "str", "topic": "manual_download", "topicType": "msg", "x": 150, "y": 1440, "wires": [["prepare-download"]]}, {"id": "clear-button", "type": "ui_button", "z": "main-tab", "g": "160fa0e161ed43df", "name": "Clear Button", "group": "group-status", "order": 2, "width": 0, "height": 0, "passthru": false, "label": "Clear Status", "tooltip": "Clear status messages", "color": "", "bgcolor": "", "className": "", "icon": "", "payload": "", "payloadType": "str", "topic": "clear", "topicType": "msg", "x": 110, "y": 1660, "wires": [["clear-status"]]}, {"id": "clear-status", "type": "function", "z": "main-tab", "g": "160fa0e161ed43df", "name": "Clear Status", "func": "msg.payload = 'Ready...';\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 410, "y": 1660, "wires": [["status-feedback"]]}, {"id": "trigger-download-endpoint", "type": "http in", "z": "main-tab", "g": "2f15178f07fb03e9", "name": "Trigger Download", "url": "/trigger-download", "method": "get", "upload": false, "swaggerDoc": "", "x": 140, "y": 1360, "wires": [["prepare-download"]]}, {"id": "trigger-download-response", "type": "http response", "z": "main-tab", "g": "2f15178f07fb03e9", "name": "Download Response", "statusCode": "", "headers": {}, "x": 1760, "y": 1360, "wires": []}, {"id": "client-download-button", "type": "ui_button", "z": "main-tab", "g": "2f15178f07fb03e9", "name": "Client Download Button", "group": "group-download", "order": 4, "width": 0, "height": 0, "passthru": false, "label": "Download to This System", "tooltip": "Download AAS file directly to your local system (Mac/PC)", "color": "", "bgcolor": "#28a745", "className": "", "icon": "", "payload": "", "payloadType": "str", "topic": "client_download", "topicType": "msg", "x": 150, "y": 1200, "wires": [["prepare-client-download"]]}, {"id": "prepare-client-download", "type": "function", "z": "main-tab", "g": "2f15178f07fb03e9", "name": "Prepare Client Download", "func": "const selectedAASId = flow.get('selectedAASId');\nconst selectedIdShort = flow.get('selectedAASIdShort');\n\nif (!selectedAASId) {\n    msg.payload = '❌ No AAS selected. Please select one from the list first.';\n    return [null, msg];\n}\n\nconst encodedId = Buffer.from(selectedAASId).toString('base64');\nconst filename = `${selectedIdShort || 'AAS'}_${new Date().toISOString().split('T')[0]}.aasx`;\n\nmsg.url = `http://aas-env:8081/serialization?aasIds=${encodedId}&includeConceptDescriptions=true`;\nmsg.filename = filename;\nmsg.selectedId = selectedAASId;\nmsg.selectedIdShort = selectedIdShort;\nmsg.isClientDownload = true;\n\nnode.log(`Preparing client download for: ${selectedIdShort} (${selectedAASId})`);\nreturn [msg, null];", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 420, "y": 1200, "wires": [["fetch-aasx"], ["status-feedback"]]}, {"id": "client-download-endpoint", "type": "http in", "z": "main-tab", "g": "2f15178f07fb03e9", "name": "Client Download Endpoint", "url": "/download-aasx", "method": "get", "upload": false, "swaggerDoc": "", "x": 160, "y": 1160, "wires": [["client-download-response"]]}, {"id": "client-download-response", "type": "function", "z": "main-tab", "g": "2f15178f07fb03e9", "name": "Client Download Response", "func": "// Handle client download request with URL parameters\nlet aasId = msg.req.query.aasId || flow.get('selectedAASId');\nlet idShort = msg.req.query.idShort || flow.get('selectedAASIdShort');\n\nif (!aasId) {\n    msg.statusCode = 400;\n    msg.payload = 'No AAS ID provided';\n    return msg;\n}\n\nconst encodedId = Buffer.from(aasId).toString('base64');\nconst filename = `${idShort || 'AAS'}_${new Date().toISOString().split('T')[0]}.aasx`;\n\n// Store download info in flow context so it survives the HTTP request\nflow.set('currentDownload', {\n    aasId: aasId,\n    idShort: idShort,\n    filename: filename\n});\n\n// Forward the request to the AAS environment\nmsg.url = `http://aas-env:8081/serialization?aasIds=${encodedId}&includeConceptDescriptions=true`;\nmsg.method = 'GET';\n\nnode.log(`Preparing download for client: ${filename} from AAS ID: ${aasId}`);\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 450, "y": 1160, "wires": [["fetch-client-aasx"]]}, {"id": "fetch-client-aasx", "type": "http request", "z": "main-tab", "g": "2f15178f07fb03e9", "name": "Fetch Client AASX", "method": "GET", "ret": "bin", "paytoqs": "ignore", "url": "", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": [{"keyType": "other", "keyValue": "Accept", "valueType": "other", "valueValue": "application/asset-administration-shell-package+xml"}], "x": 720, "y": 1160, "wires": [["prepare-client-headers"]]}, {"id": "send-client-file", "type": "http response", "z": "main-tab", "g": "2f15178f07fb03e9", "name": "Send File to Client", "statusCode": "", "headers": {}, "x": 1510, "y": 1160, "wires": []}, {"id": "prepare-client-headers", "type": "function", "z": "main-tab", "g": "2f15178f07fb03e9", "name": "Prepare Client Headers", "func": "// Get AAS info from flow context (stored before HTTP request)\nconst downloadInfo = flow.get('currentDownload') || {};\nconst filename = downloadInfo.filename || 'AAS_download.aasx';\n\n// Set proper download headers (same as working test)\nmsg.headers = {\n    'Content-Type': 'application/octet-stream',\n    'Content-Disposition': `attachment; filename=\"${filename}\"`,\n    'Cache-Control': 'no-cache, no-store, must-revalidate',\n    'Pragma': 'no-cache',\n    'Expires': '0',\n    'Access-Control-Allow-Origin': '*',\n    'Access-Control-Expose-Headers': 'Content-Disposition'\n};\n\n// Ensure we have binary data\nif (msg.statusCode === 200 && msg.payload) {\n    node.log(`Preparing client download: ${filename}, size: ${msg.payload.length} bytes`);\n    // Clear the stored download info\n    flow.set('currentDownload', null);\n} else {\n    node.error(`Failed to fetch AASX: Status ${msg.statusCode}`);\n    msg.statusCode = 500;\n    msg.payload = 'Failed to fetch AAS file';\n}\n\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1050, "y": 1160, "wires": [["send-client-file"]]}, {"id": "385dcbbf39aa02e6", "type": "debug", "z": "main-tab", "g": "3681d2701a20f874", "name": "debug 1", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "payload", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 820, "y": 820, "wires": []}, {"id": "test-download-button", "type": "ui_button", "z": "main-tab", "g": "test-download-group", "name": "Test Download <PERSON><PERSON>", "group": "group-test", "order": 1, "width": 0, "height": 0, "passthru": false, "label": "🧪 Test Download to Mac", "tooltip": "Download a test file to verify download functionality works on your Mac", "color": "", "bgcolor": "#ff6b6b", "className": "", "icon": "", "payload": "", "payloadType": "str", "topic": "test_download", "topicType": "msg", "x": 720, "y": 1800, "wires": [[]]}, {"id": "test-download-endpoint", "type": "http in", "z": "main-tab", "g": "test-download-group", "name": "Test Download Endpoint", "url": "/test-download", "method": "get", "upload": false, "swaggerDoc": "", "x": 730, "y": 1840, "wires": [["test-file-generator"]]}, {"id": "test-file-generator", "type": "function", "z": "main-tab", "g": "test-download-group", "name": "Generate Test File", "func": "// Create a simple test file with current timestamp\nconst timestamp = new Date().toISOString();\nconst filename = `test-download-${timestamp.split('T')[0]}-${timestamp.split('T')[1].split('.')[0].replace(/:/g, '-')}.txt`;\n\nconst testContent = `Test Download File\n==================\n\nGenerated on: ${timestamp}\nFrom: Node-RED Test Flow\nSystem: Mac Download Test\n\nThis file was created to test the download functionality.\nIf you can see this file in your Downloads folder, the download is working correctly!\n\n✅ Download test successful!\n\nDetails:\n- Filename: ${filename}\n- Size: ${Buffer.byteLength('test content', 'utf8')} bytes\n- Content-Type: text/plain\n- Generated by: Node-RED AAS Management System\n`;\n\n// Set response headers for file download\nmsg.headers = {\n    'Content-Type': 'text/plain',\n    'Content-Disposition': `attachment; filename=\"${filename}\"`,\n    'Cache-Control': 'no-cache, no-store, must-revalidate',\n    'Pragma': 'no-cache',\n    'Expires': '0',\n    'Access-Control-Allow-Origin': '*',\n    'Access-Control-Expose-Headers': 'Content-Disposition'\n};\n\nmsg.payload = testContent;\nmsg.statusCode = 200;\n\nnode.log(`Test download generated: ${filename}`);\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1000, "y": 1840, "wires": [["test-download-response"]]}, {"id": "test-download-response", "type": "http response", "z": "main-tab", "g": "test-download-group", "name": "Send Test File", "statusCode": "", "headers": {}, "x": 1200, "y": 1840, "wires": []}, {"id": "test-download-template", "type": "ui_template", "z": "main-tab", "g": "test-download-group", "group": "group-test", "name": "Test Download Link", "order": 2, "width": 0, "height": 0, "format": "<div style=\"text-align: center; padding: 20px; border: 2px dashed #ff6b6b; border-radius: 8px; margin: 10px 0; background: linear-gradient(135deg, #fff0f0 0%, #ffe8e8 100%);\">\n    <h3 style=\"color: #ff6b6b; margin-bottom: 15px;\">Download Test</h3>\n    <p style=\"color: #666; margin-bottom: 20px;\">Click the button below to test downloading a file to your Mac:</p>\n    \n    <button \n        onclick=\"testDownload()\" \n        style=\"\n            padding: 12px 24px;\n            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a5a 100%);\n            color: white;\n            border: none;\n            border-radius: 6px;\n            cursor: pointer;\n            font-weight: bold;\n            font-size: 16px;\n            box-shadow: 0 4px 8px rgba(255,107,107,0.3);\n            transition: all 0.2s ease;\n            margin: 5px;\n        \"\n        onmouseover=\"this.style.background='linear-gradient(135deg, #ee5a5a 0%, #dd4b4b 100%)'; this.style.transform='scale(1.05)';\"\n        onmouseout=\"this.style.background='linear-gradient(135deg, #ff6b6b 0%, #ee5a5a 100%)'; this.style.transform='scale(1)';\"\n    >\n        Test File Download\n    </button>\n    \n    <button \n        onclick=\"testAasServer()\" \n        style=\"\n            padding: 12px 24px;\n            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);\n            color: white;\n            border: none;\n            border-radius: 6px;\n            cursor: pointer;\n            font-weight: bold;\n            font-size: 16px;\n            box-shadow: 0 4px 8px rgba(23,162,184,0.3);\n            transition: all 0.2s ease;\n            margin: 5px;\n        \"\n        onmouseover=\"this.style.background='linear-gradient(135deg, #138496 0%, #0c5460 100%)'; this.style.transform='scale(1.05)';\"\n        onmouseout=\"this.style.background='linear-gradient(135deg, #17a2b8 0%, #138496 100%)'; this.style.transform='scale(1)';\"\n    >\n        Test AAS Server\n    </button>\n    \n    <p style=\"color: #888; font-size: 12px; margin-top: 15px;\">\n        This will download a small text file to verify the download functionality works on your system.\n    </p>\n    \n    <hr style=\"margin: 20px 0; border: 1px dashed #ccc;\">\n    \n    <h4 style=\"color: #666; margin-bottom: 10px;\">Direct AAS Download Links</h4>\n    <p style=\"color: #888; font-size: 12px; margin-bottom: 10px;\">\n        Right-click these links and select \"Save Link As...\" to download AAS files:\n    </p>\n    \n    <div id=\"directLinks\" style=\"font-family: monospace; font-size: 11px;\">\n        <p style=\"color: #999;\">Load AAS list first to see download links here...</p>\n    </div>\n</div>\n\n<script>\n    function testDownload() {\n        // Show notification\n        const notification = document.createElement('div');\n        notification.innerHTML = 'Starting test download to your Mac...';\n        notification.style.cssText = \n            'position: fixed; top: 20px; right: 20px; ' +\n            'background: #ff6b6b; color: white; padding: 12px 20px; ' +\n            'border-radius: 6px; box-shadow: 0 4px 8px rgba(0,0,0,0.2); ' +\n            'z-index: 1000; font-weight: bold;';\n        document.body.appendChild(notification);\n        setTimeout(() => notification.remove(), 3000);\n        \n        // Trigger download\n        const link = document.createElement('a');\n        link.href = '/test-download';\n        link.download = 'test-file.txt';\n        link.target = '_blank';\n        link.style.display = 'none';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n    }\n    \n    function testAasServer() {\n        // Show notification\n        const notification = document.createElement('div');\n        notification.innerHTML = 'Testing AAS server connection...';\n        notification.style.cssText = \n            'position: fixed; top: 20px; right: 20px; ' +\n            'background: #17a2b8; color: white; padding: 12px 20px; ' +\n            'border-radius: 6px; box-shadow: 0 4px 8px rgba(0,0,0,0.2); ' +\n            'z-index: 1000; font-weight: bold;';\n        document.body.appendChild(notification);\n        setTimeout(() => notification.remove(), 3000);\n        \n        // Trigger download\n        const link = document.createElement('a');\n        link.href = '/test-aas-server';\n        link.download = 'aas-server-test.txt';\n        link.target = '_blank';\n        link.style.display = 'none';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n    }\n</script>", "storeOutMessages": true, "fwdInMessages": true, "resendOnRefresh": true, "templateScope": "local", "className": "", "x": 720, "y": 1780, "wires": [[]]}, {"id": "simple-aas-download-endpoint", "type": "http in", "z": "main-tab", "g": "test-download-group", "name": "Simple AAS Download", "url": "/simple-aas-download", "method": "get", "upload": false, "swaggerDoc": "", "x": 730, "y": 1880, "wires": [["simple-aas-download-handler"]]}, {"id": "simple-aas-download-handler", "type": "function", "z": "main-tab", "g": "test-download-group", "name": "Simple AAS Download Handler", "func": "// Get AAS ID from query parameters\nconst aasId = msg.req.query.aasId;\nconst idShort = msg.req.query.idShort || 'AAS';\n\nif (!aasId) {\n    msg.statusCode = 400;\n    msg.payload = 'No AAS ID provided';\n    return msg;\n}\n\n// Generate filename\nconst timestamp = new Date().toISOString().split('T')[0];\nconst filename = `${idShort}_${timestamp}.aasx`;\n\nnode.log(`AAS Download Request - ID: ${aasId}, IdShort: ${idShort}`);\n\n// Create a mock AASX file content (since aas-env:8081 might not be accessible)\nconst mockAasxContent = `PK\\x03\\x04\\x14\\x00\\x00\\x00\\x08\\x00\nMOCK AASX FILE for ${idShort}\n=================================\n\nThis is a mock AASX file generated because the AAS server (aas-env:8081) \nmight not be running or accessible.\n\nAAS Information:\n- AAS ID: ${aasId}\n- ID Short: ${idShort}\n- Generated: ${new Date().toISOString()}\n- Filename: ${filename}\n\nTo get real AASX files, ensure:\n1. AAS environment server is running on aas-env:8081\n2. Docker containers are properly connected\n3. AAS server has the requested AAS ID\n\nThis mock file proves the download mechanism works!\n\nNext steps:\n1. Check docker-compose.yml for aas-env service\n2. Verify AAS server is running: docker ps\n3. Test AAS server directly: curl http://aas-env:8081/shells\n`;\n\n// Set proper download headers (same as working test)\nmsg.headers = {\n    'Content-Type': 'application/octet-stream',\n    'Content-Disposition': `attachment; filename=\"${filename}\"`,\n    'Cache-Control': 'no-cache, no-store, must-revalidate',\n    'Pragma': 'no-cache',\n    'Expires': '0',\n    'Access-Control-Allow-Origin': '*',\n    'Access-Control-Expose-Headers': 'Content-Disposition'\n};\n\nmsg.payload = Buffer.from(mockAasxContent, 'utf8');\nmsg.statusCode = 200;\n\nnode.log(`Mock AAS download prepared: ${filename}`);\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1050, "y": 1880, "wires": [["test-download-response"]]}, {"id": "simple-aas-http-request", "type": "http request", "z": "main-tab", "g": "test-download-group", "name": "Fetch AAS File", "method": "GET", "ret": "bin", "paytoqs": "ignore", "url": "", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": [], "x": 1260, "y": 1880, "wires": [["simple-aas-response"]]}, {"id": "simple-aas-response", "type": "function", "z": "main-tab", "g": "test-download-group", "name": "Prepare AAS Response", "func": "// Get download info that was stored earlier\nconst downloadInfo = msg.downloadInfo || {};\nconst filename = downloadInfo.filename || 'AAS_download.aasx';\n\nif (msg.statusCode === 200 && msg.payload) {\n    // Set proper download headers (same as working test)\n    msg.headers = {\n        'Content-Type': 'application/octet-stream',\n        'Content-Disposition': `attachment; filename=\"${filename}\"`,\n        'Cache-Control': 'no-cache, no-store, must-revalidate',\n        'Pragma': 'no-cache',\n        'Expires': '0',\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Expose-Headers': 'Content-Disposition'\n    };\n    \n    node.log(`AAS download ready: ${filename}, size: ${msg.payload.length} bytes`);\n} else {\n    msg.statusCode = 500;\n    msg.payload = `Failed to fetch AAS file: ${msg.statusCode}`;\n    node.error(`AAS download failed: ${msg.statusCode}`);\n}\n\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1470, "y": 1880, "wires": [["test-download-response"]]}, {"id": "test-aas-server-endpoint", "type": "http in", "z": "main-tab", "g": "test-download-group", "name": "Test AAS Server", "url": "/test-aas-server", "method": "get", "upload": false, "swaggerDoc": "", "x": 730, "y": 1920, "wires": [["test-aas-server-handler"]]}, {"id": "test-aas-server-handler", "type": "function", "z": "main-tab", "g": "test-download-group", "name": "Test AAS Server Connection", "func": "// Test if we can reach the AAS server\nconst testAasId = 'https://example.com/test';\nconst encodedId = Buffer.from(testAasId).toString('base64');\nconst aasUrl = `http://aas-env:8081/serialization?aasIds=${encodedId}&includeConceptDescriptions=true`;\n\nnode.log(`Testing AAS server connection to: ${aasUrl}`);\n\n// Create a simple test response showing the connection attempt\nconst testContent = `AAS Server Connection Test\n==========================\n\nTesting connection to: aas-env:8081\nAAS URL: ${aasUrl}\nEncoded Test ID: ${encodedId}\n\nGenerated at: ${new Date().toISOString()}\n\nIf you can download this file, Node-RED is working.\nIf the AAS download still fails, the issue is with:\n1. AAS server connection (aas-env:8081)\n2. Authentication/headers\n3. AAS ID encoding\n\nNext step: Check Node-RED logs for HTTP request errors.\n`;\n\n// Set same headers as working test\nmsg.headers = {\n    'Content-Type': 'text/plain',\n    'Content-Disposition': 'attachment; filename=\"aas-server-test.txt\"',\n    'Cache-Control': 'no-cache, no-store, must-revalidate',\n    'Pragma': 'no-cache',\n    'Expires': '0',\n    'Access-Control-Allow-Origin': '*',\n    'Access-Control-Expose-Headers': 'Content-Disposition'\n};\n\nmsg.payload = testContent;\nmsg.statusCode = 200;\n\nnode.log('AAS server test response prepared');\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1020, "y": 1920, "wires": [["test-download-response"]]}, {"id": "fetch-erp-data-button", "type": "ui_button", "z": "main-tab", "g": "erp-main-group", "name": "Fetch ERP Data Button", "group": "group-erp-create", "order": 1, "width": 0, "height": 0, "passthru": false, "label": "Fetch UR10 Data from ERP", "tooltip": "Fetch UR10 product data from mock ERP API", "color": "", "bgcolor": "#17a2b8", "className": "", "icon": "", "payload": "", "payloadType": "str", "topic": "fetch_erp", "topicType": "msg", "x": 150, "y": 2180, "wires": [["fetch-ur10-data"]]}, {"id": "fetch-ur10-data", "type": "http request", "z": "main-tab", "g": "erp-main-group", "name": "Fetch UR10 Data", "method": "GET", "ret": "obj", "paytoqs": "ignore", "url": "http://*************:8069/api/aas/nameplate/350", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": [], "x": 390, "y": 2180, "wires": [["process-erp-data"]]}, {"id": "process-erp-data", "type": "function", "z": "main-tab", "g": "erp-main-group", "name": "Process ERP Data", "func": "if (msg.statusCode === 200 && msg.payload && msg.payload.status === 'success') {\n    const erpTemplate = msg.payload.template;\n    \n    // Store ERP data for AAS creation\n    flow.set('erpAASTemplate', erpTemplate.aas);\n    flow.set('erpSubmodelTemplate', erpTemplate.submodel);\n    flow.set('erpData', erpTemplate.erp_data);\n    flow.set('hasERPData', true);\n    \n    const productInfo = erpTemplate.erp_data;\n    \n    const statusMsg = {\n        payload: `✅ UR10 ERP Data Loaded Successfully!\\n` +\n                 `Product: ${productInfo.productDesignation}\\n` +\n                 `Serial: ${productInfo.serialNumber}\\n` +\n                 `Manufacturer: ${productInfo.manufacturerName.en}\\n` +\n                 `Year: ${productInfo.yearOfConstruction}\\n` +\n                 `Now fetching Digital Nameplate template...`\n    };\n    \n    const templateMsg = {\n        url: 'http://aas-env:8081/submodels',\n        method: 'GET',\n        headers: {'Content-Type': 'application/json'},\n        fetchTemplate: true\n    };\n    \n    return [statusMsg, templateMsg];\n} else {\n    const errorMsg = {\n        payload: `❌ Failed to fetch ERP data. Status: ${msg.statusCode}. Check if ERP API server is running on port 8090.`\n    };\n    return [errorMsg, null];\n}", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 630, "y": 2180, "wires": [["erp-status-display"], ["fetch-nameplate-template"]]}, {"id": "fetch-nameplate-template", "type": "http request", "z": "main-tab", "g": "erp-main-group", "name": "Fetch Nameplate Template", "method": "GET", "ret": "obj", "paytoqs": "ignore", "url": "", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": [], "x": 890, "y": 2180, "wires": [["process-nameplate-template"]]}, {"id": "process-nameplate-template", "type": "function", "z": "main-tab", "g": "erp-main-group", "name": "Process Nameplate Template", "func": "if (msg.fetchTemplate && msg.statusCode === 200 && msg.payload) {\n    let submodels = msg.payload;\n    \n    // Handle BaSyx paging response format\n    if (submodels && submodels.result) {\n        submodels = submodels.result;\n    }\n    \n    // Look for existing Digital Nameplate template\n    let nameplateTemplate = null;\n    \n    for (let submodel of submodels) {\n        // Check by semantic ID\n        if (submodel.semanticId && \n            submodel.semanticId.keys && \n            submodel.semanticId.keys.length > 0 &&\n            submodel.semanticId.keys[0].value === 'https://admin-shell.io/zvei/nameplate/1/0/Nameplate') {\n            nameplateTemplate = submodel;\n            node.log(`Found Digital Nameplate template by semantic ID: ${submodel.idShort}`);\n            break;\n        }\n        \n        // Also check by idShort for SampleDigitalNameplateAAS\n        if (submodel.idShort === 'DigitalNameplate') {\n            nameplateTemplate = submodel;\n            node.log(`Found DigitalNameplate template by idShort`);\n            break;\n        }\n    }\n    \n    if (nameplateTemplate) {\n        // Store the complete template structure\n        flow.set('nameplateTemplate', nameplateTemplate);\n        flow.set('hasTemplate', true);\n        \n        const elementCount = nameplateTemplate.submodelElements ? nameplateTemplate.submodelElements.length : 0;\n        \n        msg.payload = `✅ SampleDigitalNameplateAAS template found!\\n` +\n                      `Template: ${nameplateTemplate.idShort}\\n` +\n                      `Elements: ${elementCount} (includes Phone, Fax, Markings, etc.)\\n` +\n                      `ID: ${nameplateTemplate.id}\\n` +\n                      `Ready to merge with ERP data while preserving all sections.`;\n    } else {\n        // No template found, will use basic structure\n        flow.set('hasTemplate', false);\n        msg.payload = `⚠️ No SampleDigitalNameplateAAS template found.\\n` +\n                      `Will create basic structure with ERP data.\\n` +\n                      `Available submodels: ${submodels.map(s => s.idShort || 'Unknown').join(', ')}`;\n    }\n    \n    return msg;\n} else {\n    return null;\n}", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1170, "y": 2180, "wires": [["erp-status-display"]]}, {"id": "create-erp-aas-button", "type": "ui_button", "z": "main-tab", "g": "erp-main-group", "name": "Create ERP AAS Button", "group": "group-erp-create", "order": 2, "width": 0, "height": 0, "passthru": false, "label": "Create UR10 AAS from ERP Data", "tooltip": "Create AAS using fetched ERP data and Digital Nameplate template", "color": "", "bgcolor": "#28a745", "className": "", "icon": "", "payload": "", "payloadType": "str", "topic": "create_erp_aas", "topicType": "msg", "x": 150, "y": 2280, "wires": [["prepare-erp-aas-creation"]]}, {"id": "prepare-erp-aas-creation", "type": "function", "z": "main-tab", "g": "erp-main-group", "name": "Prepare ERP AAS Creation", "func": "const hasERPData = flow.get('hasERPData');\nconst erpAASTemplate = flow.get('erpAASTemplate');\nconst erpData = flow.get('erpData');\n\nif (!hasERPData || !erpAASTemplate || !erpData) {\n    msg.payload = '❌ No ERP data available. Please fetch UR10 data first.';\n    return [null, msg];\n}\n\n// Use the ERP template AAS structure\nconst aasData = erpAASTemplate;\n\n// Store data for submodel creation\nflow.set('newERPAASData', aasData);\nflow.set('needsERPSubmodel', true);\n\nmsg.payload = aasData;\nmsg.url = 'http://aas-env:8081/shells';\nmsg.method = 'POST';\nmsg.headers = {\n    'Content-Type': 'application/json'\n};\n\nnode.log(`Creating AAS: ${aasData.idShort} with ID: ${aasData.id}`);\nreturn [msg, null];", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 470, "y": 2280, "wires": [["register-erp-aas"], ["erp-status-display"]]}, {"id": "register-erp-aas", "type": "http request", "z": "main-tab", "g": "erp-main-group", "name": "Register ERP AAS", "method": "POST", "ret": "obj", "paytoqs": "ignore", "url": "", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": {}, "x": 730, "y": 2280, "wires": [["handle-erp-aas-response"]]}, {"id": "handle-erp-aas-response", "type": "function", "z": "main-tab", "g": "erp-main-group", "name": "Handle ERP AAS Response", "func": "if (msg.statusCode === 201 || msg.statusCode === 200) {\n    const aasData = msg.payload;\n    flow.set('createdERPAAS', aasData);\n    \n    // Check if we need to create submodel\n    const needsERPSubmodel = flow.get('needsERPSubmodel');\n    const newERPAASData = flow.get('newERPAASData');\n    const erpData = flow.get('erpData');\n    \n    if (needsERPSubmodel && newERPAASData && erpData) {\n        // Prepare to create DigitalNameplate submodel with ERP data\n        const submodelId = newERPAASData.id.replace('/aas/', '/sm/') + '/DigitalNameplate';\n        \n        msg.payload = `✅ UR10 AAS '${aasData.idShort}' created! Creating Digital Nameplate with ERP data...`;\n        \n        // Clear flags\n        flow.set('needsERPSubmodel', false);\n        \n        // Trigger submodel creation\n        setTimeout(() => {\n            node.send([null, {\n                submodelId: submodelId,\n                aasIdShort: aasData.idShort,\n                erpData: erpData,\n                isERPCreate: true\n            }]);\n        }, 1000);\n        \n        return [msg, null];\n    } else {\n        msg.payload = `✅ ERP AAS '${aasData.idShort}' created successfully!`;\n        return [msg, null];\n    }\n} else {\n    msg.payload = `❌ Failed to create ERP AAS. Status: ${msg.statusCode}. Error: ${JSON.stringify(msg.payload)}`;\n    return [msg, null];\n}", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 990, "y": 2280, "wires": [["erp-status-display", "refresh-aas-list"], ["create-erp-submodel"]]}, {"id": "create-erp-submodel", "type": "function", "z": "main-tab", "g": "erp-main-group", "name": "Create ERP Submodel", "func": "// Template-Based ERP Submodel Creation\n// This function fetches existing Digital Nameplate templates and merges ERP data\n\nconst submodelId = msg.submodelId;\nconst aasIdShort = msg.aasIdShort;\nconst erpData = msg.erpData;\nconst isERPCreate = msg.isERPCreate || false;\n\nif (!submodelId || !erpData) {\n    return null;\n}\n\n// Get the fetched template if available\nconst hasTemplate = flow.get('hasTemplate');\nconst nameplateTemplate = flow.get('nameplateTemplate');\n\nlet submodelData;\n\nif (hasTemplate && nameplateTemplate) {\n    // Use the fetched template as base\n    submodelData = JSON.parse(JSON.stringify(nameplateTemplate)); // Deep copy\n    \n    // Update the ID and basic info\n    submodelData.id = submodelId;\n    submodelData.idShort = \"DigitalNameplate\";\n    \n    // Function to update element value by semantic ID\n    function updateElementBySemanticId(elements, semanticId, newValue, valueType = null) {\n        for (let element of elements) {\n            if (element.semanticId && \n                element.semanticId.keys && \n                element.semanticId.keys.length > 0 &&\n                element.semanticId.keys[0].value === semanticId) {\n                \n                if (element.modelType === 'MultiLanguageProperty') {\n                    element.value = newValue;\n                } else if (element.modelType === 'Property') {\n                    element.value = newValue;\n                    if (valueType) element.valueType = valueType;\n                }\n                return true;\n            }\n            \n            // Recursively check SubmodelElementCollections\n            if (element.modelType === 'SubmodelElementCollection' && element.value) {\n                if (updateElementBySemanticId(element.value, semanticId, newValue, valueType)) {\n                    return true;\n                }\n            }\n        }\n        return false;\n    }\n    \n    // Update template with ERP data\n    if (submodelData.submodelElements) {\n        // ManufacturerName\n        updateElementBySemanticId(submodelData.submodelElements, '0173-1#02-AAO677#002', [\n            {\"language\": \"en\", \"text\": erpData.manufacturerName.en},\n            {\"language\": \"de\", \"text\": erpData.manufacturerName.de}\n        ]);\n        \n        // ManufacturerProductDesignation\n        updateElementBySemanticId(submodelData.submodelElements, '0173-1#02-AAW338#001', [\n            {\"language\": \"en\", \"text\": erpData.productDesignation},\n            {\"language\": \"de\", \"text\": erpData.productDesignation}\n        ]);\n        \n        // ManufacturerProductFamily\n        updateElementBySemanticId(submodelData.submodelElements, '0173-1#02-AAU731#001', [\n            {\"language\": \"en\", \"text\": erpData.productFamily},\n            {\"language\": \"de\", \"text\": erpData.productFamily}\n        ]);\n        \n        // SerialNumber\n        updateElementBySemanticId(submodelData.submodelElements, '0173-1#02-AAM556#002', \n            erpData.serialNumber, 'xs:string');\n        \n        // YearOfConstruction\n        updateElementBySemanticId(submodelData.submodelElements, '0173-1#02-AAP906#001', \n            erpData.yearOfConstruction, 'xs:string');\n        \n        // Address elements\n        if (erpData.address) {\n            updateElementBySemanticId(submodelData.submodelElements, '0173-1#02-AAO128#002', [\n                {\"language\": \"en\", \"text\": erpData.address.street},\n                {\"language\": \"de\", \"text\": erpData.address.street}\n            ]);\n            updateElementBySemanticId(submodelData.submodelElements, '0173-1#02-AAO129#002', [\n                {\"language\": \"en\", \"text\": erpData.address.zipcode},\n                {\"language\": \"de\", \"text\": erpData.address.zipcode}\n            ]);\n            updateElementBySemanticId(submodelData.submodelElements, '0173-1#02-AAO132#002', [\n                {\"language\": \"en\", \"text\": erpData.address.city},\n                {\"language\": \"de\", \"text\": erpData.address.city}\n            ]);\n            updateElementBySemanticId(submodelData.submodelElements, '0173-1#02-AAO133#002', [\n                {\"language\": \"en\", \"text\": erpData.address.state},\n                {\"language\": \"de\", \"text\": erpData.address.state}\n            ]);\n            updateElementBySemanticId(submodelData.submodelElements, '0173-1#02-AAO134#002', [\n                {\"language\": \"en\", \"text\": erpData.address.country},\n                {\"language\": \"de\", \"text\": erpData.address.country}\n            ]);\n        }\n        \n        // Email\n        if (erpData.email) {\n            updateElementBySemanticId(submodelData.submodelElements, '0173-1#02-AAO198#002', \n                erpData.email, 'xs:string');\n        }\n        \n        // Phone Number - THIS WAS MISSING!\n        if (erpData.phone) {\n            updateElementBySemanticId(submodelData.submodelElements, '0173-1#02-AAO136#002', [\n                {\"language\": \"en\", \"text\": erpData.phone},\n                {\"language\": \"de\", \"text\": erpData.phone}\n            ]);\n        }\n    }\n    \n    node.log(`✅ Using template with ${submodelData.submodelElements ? submodelData.submodelElements.length : 0} elements`);\n    \n} else {\n    // Fallback: Create basic structure if no template found\n    node.log(`⚠️ No template found, creating basic structure`);\n    submodelData = {\n        \"modelType\": \"Submodel\",\n        \"kind\": \"Instance\",\n        \"semanticId\": {\n            \"keys\": [{\n                \"type\": \"Submodel\",\n                \"value\": \"https://admin-shell.io/zvei/nameplate/1/0/Nameplate\"\n            }],\n            \"type\": \"ModelReference\"\n        },\n        \"id\": submodelId,\n        \"idShort\": \"DigitalNameplate\",\n        \"submodelElements\": [\n            {\n                \"modelType\": \"MultiLanguageProperty\",\n                \"semanticId\": {\n                    \"keys\": [{\"type\": \"GlobalReference\", \"value\": \"0173-1#02-AAO677#002\"}],\n                    \"type\": \"ExternalReference\"\n                },\n                \"value\": [\n                    {\"language\": \"en\", \"text\": erpData.manufacturerName.en},\n                    {\"language\": \"de\", \"text\": erpData.manufacturerName.de}\n                ],\n                \"category\": \"PARAMETER\",\n                \"idShort\": \"ManufacturerName\"\n            },\n            {\n                \"modelType\": \"MultiLanguageProperty\",\n                \"semanticId\": {\n                    \"keys\": [{\"type\": \"GlobalReference\", \"value\": \"0173-1#02-AAW338#001\"}],\n                    \"type\": \"ExternalReference\"\n                },\n                \"value\": [\n                    {\"language\": \"en\", \"text\": erpData.productDesignation},\n                    {\"language\": \"de\", \"text\": erpData.productDesignation}\n                ],\n                \"category\": \"PARAMETER\",\n                \"idShort\": \"ManufacturerProductDesignation\"\n            },\n            {\n                \"modelType\": \"Property\",\n                \"semanticId\": {\n                    \"keys\": [{\"type\": \"GlobalReference\", \"value\": \"0173-1#02-AAM556#002\"}],\n                    \"type\": \"ExternalReference\"\n                },\n                \"value\": erpData.serialNumber,\n                \"valueType\": \"xs:string\",\n                \"category\": \"PARAMETER\",\n                \"idShort\": \"SerialNumber\"\n            },\n            {\n                \"modelType\": \"Property\",\n                \"semanticId\": {\n                    \"keys\": [{\"type\": \"GlobalReference\", \"value\": \"0173-1#02-AAP906#001\"}],\n                    \"type\": \"ExternalReference\"\n                },\n                \"value\": erpData.yearOfConstruction,\n                \"valueType\": \"xs:string\",\n                \"category\": \"PARAMETER\",\n                \"idShort\": \"YearOfConstruction\"\n            }\n        ]\n    };\n}\n\nmsg.payload = submodelData;\nmsg.url = 'http://aas-env:8081/submodels';\nmsg.method = 'POST';\nmsg.headers = {\n    'Content-Type': 'application/json'\n};\n\nnode.log(`Creating ERP submodel: ${submodelId}`);\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1290, "y": 2380, "wires": [["register-erp-submodel"]]}, {"id": "register-erp-submodel", "type": "http request", "z": "main-tab", "g": "erp-main-group", "name": "Register ERP Submodel", "method": "POST", "ret": "obj", "paytoqs": "ignore", "url": "", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": {}, "x": 1550, "y": 2380, "wires": [["handle-erp-submodel-response"]]}, {"id": "handle-erp-submodel-response", "type": "function", "z": "main-tab", "g": "erp-main-group", "name": "Handle ERP Submodel Response", "func": "if (msg.statusCode === 201 || msg.statusCode === 200) {\n    const erpData = flow.get('erpData');\n    const filename = `UR10_Robot_DigitalNameplate_${erpData.serialNumber}.aasx`;\n    \n    msg.payload = `✅ Complete UR10 AAS created from ERP data!\\n` +\n                  `Product: ${erpData.productDesignation}\\n` +\n                  `Serial: ${erpData.serialNumber}\\n` +\n                  `Manufacturer: ${erpData.manufacturerName.en}\\n` +\n                  `Ready for download as: ${filename}`;\n} else {\n    msg.payload = `⚠️ ERP AAS created but submodel failed. Status: ${msg.statusCode}`;\n}\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1810, "y": 2380, "wires": [["erp-status-display"]]}, {"id": "erp-status-display", "type": "ui_text", "z": "main-tab", "g": "erp-main-group", "group": "group-erp-create", "order": 3, "width": "12", "height": "7", "name": "ERP Status Display", "label": "ERP Integration Status:", "format": "{{msg.payload}}", "layout": "row-spread", "className": "", "style": false, "font": "", "fontSize": "", "color": "#000000", "x": 1030, "y": 2480, "wires": []}, {"id": "automated-aas-endpoint", "type": "http in", "z": "main-tab", "g": "test-download-group", "name": "Automated AAS Creation", "url": "/api/aas/create-and-download", "method": "post", "upload": false, "swaggerDoc": "", "x": 680, "y": 1800, "wires": [["automated-aas-handler"]]}, {"id": "automated-aas-handler", "type": "function", "z": "main-tab", "g": "test-download-group", "name": "Handle Automated Request", "func": "// Extract product_id from the request payload\nconst payload = msg.payload || {};\nconst productId = payload.product_id;\nconst productCode = payload.product_code || `PROD_${productId}`;\nconst productName = payload.product_name || 'Unknown Product';\nconst erpApiUrl = payload.erp_api_url;\n\nif (!productId) {\n    msg.statusCode = 400;\n    msg.payload = {\n        status: 'error',\n        message: 'product_id is required',\n        timestamp: new Date().toISOString()\n    };\n    return [null, msg];\n}\n\nif (!erpApiUrl) {\n    msg.statusCode = 400;\n    msg.payload = {\n        status: 'error',\n        message: 'erp_api_url is required',\n        timestamp: new Date().toISOString()\n    };\n    return [null, msg];\n}\n\n// Store automation context\nflow.set('automatedProductId', productId);\nflow.set('automatedProductCode', productCode);\nflow.set('automatedProductName', productName);\nflow.set('automatedErpApiUrl', erpApiUrl);\nflow.set('automatedStartTime', Date.now());\n\nnode.log(`Starting automated AAS creation for product ${productId}: ${productName}`);\n\n// Prepare ERP data fetch with JSON-RPC format\nmsg.url = erpApiUrl;\nmsg.method = 'POST';\nmsg.headers = {'Content-Type': 'application/json'};\nmsg.payload = {\n    \"jsonrpc\": \"2.0\",\n    \"method\": \"call\",\n    \"params\": {\n        \"product_id\": productId\n    },\n    \"id\": 1\n};\n\nreturn [msg, null];", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 920, "y": 1800, "wires": [["automated-fetch-erp"], ["automated-download-response"]]}, {"id": "automated-fetch-erp", "type": "http request", "z": "main-tab", "g": "test-download-group", "name": "Fetch ERP Data", "method": "POST", "ret": "obj", "paytoqs": "ignore", "url": "", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": [], "x": 1160, "y": 1800, "wires": [["automated-create-aas"]]}, {"id": "automated-create-aas", "type": "function", "z": "main-tab", "g": "test-download-group", "name": "Create AAS from ERP Data", "func": "const productId = flow.get('automatedProductId');\nconst productCode = flow.get('automatedProductCode');\nconst productName = flow.get('automatedProductName');\n\n// Handle JSON-RPC response from Odoo ERP API\nif (msg.statusCode !== 200 || !msg.payload || !msg.payload.jsonrpc || !msg.payload.result) {\n    msg.statusCode = 500;\n    msg.payload = {\n        status: 'error',\n        message: `Failed to fetch ERP data: ${msg.payload ? (msg.payload.error ? msg.payload.error.message : 'Invalid response format') : 'Unknown error'}`,\n        product_id: productId,\n        timestamp: new Date().toISOString()\n    };\n    return [null, msg];\n}\n\nconst erpResult = msg.payload.result;\nif (erpResult.status !== 'success' || !erpResult.template) {\n    msg.statusCode = 500;\n    msg.payload = {\n        status: 'error',\n        message: `ERP API error: ${erpResult.message || 'Template not found'}`,\n        product_id: productId,\n        timestamp: new Date().toISOString()\n    };\n    return [null, msg];\n}\n\nconst erpTemplate = erpResult.template;\nconst erpData = erpTemplate.erp_data;\n\nnode.log(`Real ERP data received for product ${productId}: ${productName}`);\nnode.log(`Manufacturer: ${erpData.manufacturerName.en}`);\n\n// Store ERP data for submodel creation\nflow.set('automatedErpData', erpData);\nflow.set('automatedErpTemplate', erpTemplate);\n\n// Create AAS using ERP template\nconst aasData = erpTemplate.aas;\n\n// Store for submodel creation\nflow.set('automatedAasData', aasData);\n\nnode.log(`Creating automated AAS: ${aasData.idShort} for product ${productId}`);\n\n// Send to BaSyx server\nmsg.payload = aasData;\nmsg.url = 'http://aas-env:8081/shells';\nmsg.method = 'POST';\nmsg.headers = {\n    'Content-Type': 'application/json'\n};\n\nreturn [msg, null];", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1420, "y": 1800, "wires": [["automated-create-aas-http"], ["automated-download-response"]]}, {"id": "automated-create-aas-http", "type": "http request", "z": "main-tab", "g": "test-download-group", "name": "Create AAS HTTP", "method": "POST", "ret": "obj", "paytoqs": "ignore", "url": "", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": [], "x": 1680, "y": 1800, "wires": [["automated-create-submodel"]]}, {"id": "automated-create-submodel", "type": "function", "z": "main-tab", "g": "test-download-group", "name": "Create Submodel", "func": "const productId = flow.get('automatedProductId');\nconst erpData = flow.get('automatedErpData');\nconst erpTemplate = flow.get('automatedErpTemplate');\nconst aasData = flow.get('automatedAasData');\n\nif (msg.statusCode !== 201 && msg.statusCode !== 200) {\n    msg.statusCode = 500;\n    msg.payload = {\n        status: 'error',\n        message: `Failed to create AAS: ${JSON.stringify(msg.payload)}`,\n        product_id: productId,\n        timestamp: new Date().toISOString()\n    };\n    return [null, msg];\n}\n\nnode.log(`AAS created successfully, now creating submodel for product ${productId}`);\n\n// Create submodel using ERP template\nconst submodelData = erpTemplate.submodel;\n\n// Send to BaSyx server\nmsg.payload = submodelData;\nmsg.url = 'http://aas-env:8081/submodels';\nmsg.method = 'POST';\nmsg.headers = {\n    'Content-Type': 'application/json'\n};\n\nreturn [msg, null];", "outputs": 2, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1160, "y": 1900, "wires": [["automated-create-submodel-http"], ["automated-download-response"]]}, {"id": "automated-create-submodel-http", "type": "http request", "z": "main-tab", "g": "test-download-group", "name": "Create Submodel HTTP", "method": "POST", "ret": "obj", "paytoqs": "ignore", "url": "", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": [], "x": 1420, "y": 1900, "wires": [["automated-download-response"]]}, {"id": "automated-download-response", "type": "function", "z": "main-tab", "g": "test-download-group", "name": "Send Response", "func": "const productId = flow.get('automatedProductId');\nconst productName = flow.get('automatedProductName');\nconst productCode = flow.get('automatedProductCode');\nconst startTime = flow.get('automatedStartTime');\nconst duration = Date.now() - startTime;\nconst aasData = flow.get('automatedAasData');\n\n// Check if this is a successful submodel creation\nif (msg.statusCode === 201 || msg.statusCode === 200) {\n    // Success - AAS and submodel created on BaSyx server\n    node.log(`AAS and submodel created successfully for product ${productId}`);\n    \n    msg.statusCode = 200;\n    msg.payload = {\n        status: 'success',\n        message: `AAS created successfully for product ${productId}`,\n        product_id: productId,\n        product_name: productName,\n        product_code: productCode,\n        basyx_server: 'http://aas-env:8081',\n        aas_id: aasData ? aasData.id : null,\n        duration_ms: duration,\n        timestamp: new Date().toISOString()\n    };\n    \n    node.log(`Automated AAS creation completed for product ${productId} in ${duration}ms`);\n} else if (msg.payload && msg.payload.status === 'error') {\n    // Error already formatted\n    // Keep existing error message\n} else {\n    // Generic error\n    msg.statusCode = 500;\n    msg.payload = {\n        status: 'error',\n        message: 'Unknown error occurred during AAS creation',\n        product_id: productId,\n        duration_ms: duration,\n        timestamp: new Date().toISOString()\n    };\n    \n    node.log(`Automated AAS creation failed for product ${productId} in ${duration}ms`);\n}\n\n// Clean up flow variables\nflow.set('automatedProductId', null);\nflow.set('automatedProductCode', null);\nflow.set('automatedProductName', null);\nflow.set('automatedErpApiUrl', null);\nflow.set('automatedStartTime', null);\nflow.set('automatedErpData', null);\nflow.set('automatedErpTemplate', null);\nflow.set('automatedAasData', null);\n\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 920, "y": 1900, "wires": [["http-response-automated"]]}, {"id": "automated-download-aasx", "type": "http request", "z": "main-tab", "g": "test-download-group", "name": "Download AASX", "method": "GET", "ret": "bin", "paytoqs": "ignore", "url": "", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": [], "x": 1160, "y": 1840, "wires": [["automated-final-response"]]}, {"id": "automated-final-response", "type": "function", "z": "main-tab", "g": "test-download-group", "name": "Final Response", "func": "const context = flow.get('automatedDownloadContext');\n\nif (msg.statusCode === 200 && msg.payload) {\n    // Success - AASX file downloaded\n    const filename = `${context.productCode || context.productId}_AAS.aasx`;\n    \n    msg.statusCode = 200;\n    msg.headers = {\n        'Content-Type': 'application/asset-administration-shell-package+xml',\n        'Content-Disposition': `attachment; filename=\"${filename}\"`,\n        'Content-Length': msg.payload.length\n    };\n    \n    node.log(`AASX file downloaded successfully for product ${context.productId}: ${filename} (${msg.payload.length} bytes)`);\n} else {\n    // Error downloading AASX\n    msg.statusCode = 500;\n    msg.payload = {\n        status: 'error',\n        message: 'Failed to download AASX file from BaSyx server',\n        product_id: context.productId,\n        timestamp: new Date().toISOString()\n    };\n    \n    node.log(`Failed to download AASX for product ${context.productId}`);\n}\n\n// Clean up flow variables\nflow.set('automatedProductId', null);\nflow.set('automatedProductCode', null);\nflow.set('automatedProductName', null);\nflow.set('automatedErpApiUrl', null);\nflow.set('automatedStartTime', null);\nflow.set('automatedErpData', null);\nflow.set('automatedErpTemplate', null);\nflow.set('automatedAasData', null);\nflow.set('automatedDownloadContext', null);\n\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1160, "y": 1780, "wires": [["http-response-automated"]]}, {"id": "http-response-automated", "type": "http response", "z": "main-tab", "g": "test-download-group", "name": "HTTP Response", "statusCode": "", "headers": {}, "x": 1160, "y": 1940, "wires": []}]