# ERP Integration Workflow for AAS Automation

## Overview

This document describes the comprehensive ERP integration workflow that demonstrates automated AAS (Asset Administration Shell) creation from ERP product data. The implementation showcases a realistic Industry 4.0 scenario where existing ERP product data is automatically transformed into standardized AAS Digital Nameplate format.

## Architecture

### Components

1. **Mock ERP Python API Server** (`erp-integration/python/`)
   - FastAPI-based REST server
   - Serves UR10 robotic arm product data
   - Provides IDTA-compliant Digital Nameplate information
   - Runs on port 8090

2. **Node-RED Integration Flows**
   - ERP data fetching and processing
   - Template-based AAS creation
   - Data mapping with proper semantic IDs
   - Error handling and validation

3. **BaSyx AAS Environment**
   - AAS and submodel storage
   - AASX serialization/deserialization
   - Template management

## Template-Based AAS Creation

### Approach
The implementation uses a **template-first approach** that:

1. **Fetches existing Digital Nameplate templates** from BaSyx server
2. **Preserves complete template structure** including all sections (Phone, Fax, Markings, AssetSpecificProperties, etc.)
3. **Merges ERP data** into appropriate template fields using semantic ID matching
4. **Maintains template integrity** while populating available data
5. **Falls back to basic structure** if no templates are found

### Template Fetching Process
```javascript
// 1. Fetch all submodels from BaSyx
GET http://aas-env:8081/submodels

// 2. Filter for Digital Nameplate templates
semanticId: "https://admin-shell.io/zvei/nameplate/1/0/Nameplate"

// 3. Use template as base structure
// 4. Update only fields with available ERP data
// 5. Preserve all other template sections
```

### Benefits
- **Complete compliance** with IDTA Digital Nameplate standard
- **Preserves all template sections** (not just basic fields)
- **Maintains semantic consistency** with existing templates
- **Supports template evolution** without code changes
- **Ensures data completeness** beyond ERP scope

### Template Analysis Tool
Use `erp-integration/python/template_fetcher.py` to:
- Analyze existing templates in BaSyx server
- Understand template structure and elements
- Identify available semantic IDs
- Export template samples for reference

```bash
cd erp-integration/python
python template_fetcher.py
```

## ERP API Endpoints

### Base URL: `http://*************:8090`

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/` | GET | API information and available endpoints |
| `/api/products/ur10` | GET | Complete UR10 product information |
| `/api/products/ur10/nameplate` | GET | Digital Nameplate data (IDTA compliant) |
| `/api/products/ur10/specifications` | GET | Technical specifications |
| `/api/products/ur10/aas-template` | GET | AAS-ready template with semantic IDs |
| `/health` | GET | Health check endpoint |

## UR10 Product Data Structure

### Basic Information
- **Product ID**: UR10-2024-001
- **Model**: UR10
- **Manufacturer**: Universal Robots A/S
- **Serial Number**: **********
- **Year of Construction**: 2024

### Technical Specifications
- **Payload**: 10 kg
- **Reach**: 1300 mm
- **Weight**: 33.5 kg
- **Repeatability**: ±0.1 mm
- **Degrees of Freedom**: 6
- **Protection Rating**: IP54

### Company Information
- **Address**: Energivej 25, 5260 Odense S, Denmark
- **Email**: <EMAIL>
- **Website**: https://www.universal-robots.com

## Data Mapping: ERP to AAS

### IDTA Digital Nameplate Semantic IDs

| AAS Element | Semantic ID | ERP Source |
|-------------|-------------|------------|
| ManufacturerName | 0173-1#02-AAO677#002 | company_info.manufacturer_name |
| ManufacturerProductDesignation | 0173-1#02-AAW338#001 | basic_info.model + "Collaborative Robot" |
| ManufacturerProductFamily | 0173-1#02-AAU731#001 | basic_info.product_family |
| SerialNumber | 0173-1#02-AAM556#002 | basic_info.serial_number |
| YearOfConstruction | 0173-1#02-AAP906#001 | basic_info.year_of_construction |
| Address | 0173-1#02-AAQ832#005 | company_info.address |
| Email | 0173-1#02-AAQ836#005 | company_info.contact.email |

### Address Mapping

| AAS Element | Semantic ID | ERP Source |
|-------------|-------------|------------|
| Street | 0173-1#02-AAO128#002 | address.street |
| Zipcode | 0173-1#02-AAO129#002 | address.zipcode |
| CityTown | 0173-1#02-AAO132#002 | address.city |
| StateCounty | 0173-1#02-AAO133#002 | address.state |
| NationalCode | 0173-1#02-AAO134#002 | address.country |

## Workflow Steps

### 1. Start ERP API Server

```bash
# Using Docker Compose (recommended)
docker compose -f docker-compose.zema.yml up -d

# Or manually
cd erp-integration/python
python3 erp_api_server.py
```

### 2. Access Node-RED Dashboard

1. Open Node-RED: http://*************:1880
2. Navigate to the AAS Dashboard: http://*************:1880/ui
3. Locate the "Create from ERP Data" section

### 3. Fetch ERP Data

1. Click "Fetch UR10 Data from ERP"
2. System retrieves product data from ERP API
3. Data is validated and stored for AAS creation
4. Status display shows successful data loading

### 4. Create AAS from ERP Data

1. Click "Create UR10 AAS from ERP Data"
2. System creates AAS using ERP template
3. Digital Nameplate submodel is populated with ERP data
4. Final AASX file is generated: `UR10_Robot_DigitalNameplate_**********.aasx`

## Generated AAS Structure

### AAS Metadata
```json
{
  "modelType": "AssetAdministrationShell",
  "id": "https://universal-robots.com/aas/UR10_**********_20241203120000",
  "idShort": "UR10_**********",
  "description": [
    {
      "language": "en",
      "text": "Digital Nameplate AAS for UR10 Collaborative Robot **********"
    }
  ]
}
```

### Digital Nameplate Submodel
- **Semantic ID**: https://admin-shell.io/zvei/nameplate/1/0/Nameplate
- **Elements**: ManufacturerName, ProductDesignation, SerialNumber, etc.
- **Multi-language support**: English and German
- **Proper categorization**: PARAMETER vs VARIABLE

## Error Handling

### API Connection Errors
- ERP API server unavailable (port 8090)
- Network connectivity issues
- Invalid response format

### Data Validation Errors
- Missing required fields
- Invalid data types
- Semantic ID mismatches

### AAS Creation Errors
- BaSyx server unavailable
- Duplicate AAS IDs
- Submodel creation failures

## Testing and Validation

### 1. ERP API Testing
```bash
# Test API availability
curl http://*************:8090/health

# Test UR10 data endpoint
curl http://*************:8090/api/products/ur10/nameplate
```

### 2. Node-RED Flow Testing
1. Check ERP data fetching
2. Validate data mapping
3. Verify AAS creation
4. Test AASX download

### 3. BaSyx Integration Testing
1. Verify AAS registration
2. Check submodel creation
3. Test AASX serialization

## File Outputs

### Generated Files
- **AAS JSON**: Stored in BaSyx environment
- **Submodel JSON**: Digital Nameplate with ERP data
- **AASX Package**: `UR10_Robot_DigitalNameplate_**********.aasx`

### Download Location
- Server: Current working directory
- Client: Browser download folder

## Troubleshooting

### Common Issues

1. **ERP API Not Accessible**
   - Check Docker container status: `docker ps`
   - Verify port 8090 is available
   - Check container logs: `docker logs erp-api`

2. **Node-RED Flow Errors**
   - Check Node-RED logs
   - Verify flow configuration
   - Test individual nodes

3. **BaSyx Connection Issues**
   - Verify AAS environment is running
   - Check port 8081 accessibility
   - Review BaSyx logs

### Debug Commands

```bash
# Check all containers
docker ps

# View ERP API logs
docker logs erp-api

# Test ERP API directly
curl http://*************:8090/api/products/ur10/aas-template

# Check BaSyx AAS list
curl http://*************:8081/shells
```

## Benefits Demonstrated

1. **Automated Data Transformation**: ERP → AAS conversion
2. **Standardized Format**: IDTA Digital Nameplate compliance
3. **Industry 4.0 Integration**: Real-world ERP scenario
4. **Template-Based Approach**: Reusable for other products
5. **Multi-language Support**: English and German
6. **Complete Workflow**: From data fetch to AASX generation

This implementation showcases how existing enterprise systems can be integrated with modern Industry 4.0 standards through automated AAS generation.
