# Docker Compose Troubleshooting Guide - June 16, 2025

## Overview
This document provides a comprehensive guide to resolving Docker Compose errors encountered in the BaSyx AAS Environment with Node-RED integration. All issues documented here were successfully resolved.

## Initial Error State
When running `docker compose up -d`, the following errors were encountered:

```bash
time="2025-06-16T09:50:53Z" level=warning msg="/home/<USER>/projects/Thesis/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion"
failed to solve: failed to read dockerfile: open Dockerfile: no such file or directory
dependency failed to start: container mongo is unhealthy
```

## Issues Encountered and Solutions

### 1. Obsolete Docker Compose Version Attribute

**Error:**
```
the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion
```

**Cause:** Docker Compose v3.8+ no longer requires the `version` attribute in docker-compose.yml files.

**Solution:**
- **File:** `/home/<USER>/projects/Thesis/docker-compose.yml`
- **Change:** Removed the first line `version: '3.8'`
- **Result:** Warning eliminated

### 2. Missing Dockerfile for Node-RED Service

**Error:**
```
failed to solve: failed to read dockerfile: open Dockerfile: no such file or directory
```

**Cause:** Docker Compose was looking for a Dockerfile in an incorrect location.

**Original Configuration:**
```yaml
nodered:
  build: 
    context: ./nodered
    dockerfile: ../basyx-server/Dockerfile  # INCORRECT PATH
```

**Investigation:** Found the actual Dockerfile location using:
```bash
find /home/<USER>/projects/Thesis -name "Dockerfile*" -type f
# Result: /home/<USER>/projects/Thesis/nodered/.config/Dockerfile
```

**Solution:**
```yaml
nodered:
  build: 
    context: ./nodered
    dockerfile: .config/Dockerfile  # CORRECTED PATH
```

### 3. MongoDB Health Check Failures

**Error:**
```
dependency failed to start: container mongo is unhealthy
```

**Root Cause Analysis:**
MongoDB 5.0.10 was failing to start due to CPU instruction set incompatibility.

**Container Logs:**
```
WARNING: MongoDB 5.0+ requires a CPU with AVX support, and your current system does not appear to have that!
/usr/local/bin/docker-entrypoint.sh: line 412: 28 Illegal instruction (core dumped)
```

**Solutions Applied:**

#### 3.1 MongoDB Version Downgrade
- **Problem:** MongoDB 5.0+ requires AVX CPU instruction support
- **Solution:** Downgraded to MongoDB 4.4 which doesn't require AVX
- **Change:** 
  ```yaml
  # Before
  image: mongo:5.0.10
  # After  
  image: mongo:4.4
  ```

#### 3.2 MongoDB Health Check Command Fix
- **Problem:** Incorrect health check command
- **Original:** `test: mongo`
- **Fixed:** `test: ["CMD", "mongo", "--eval", "db.adminCommand('ping')"]`
- **Complete Health Check:**
  ```yaml
  healthcheck:
    test: ["CMD", "mongo", "--eval", "db.adminCommand('ping')"]
    interval: 30s
    timeout: 10s
    retries: 5
    start_period: 40s
  ```

### 4. AAS Environment Health Check Issues

**Error:**
```
dependency failed to start: container aas-env is unhealthy
```

**Root Cause:** Incorrect health check endpoint path.

**Investigation:**
- AAS Environment was running correctly on port 8081
- Health endpoint was available at `/actuator/health`, not `/health`
- Verified with: `curl -f http://localhost:8081/actuator/health` → `{"status":"UP"}`

**Solution:**
```yaml
healthcheck:
  test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8081/actuator/health"]
  interval: 30s
  timeout: 10s
  retries: 5
  start_period: 40s
```

### 5. Node-RED Dockerfile Configuration Issues

**Multiple Error Phases:**

#### 5.1 Missing settings.js File
**Error:**
```
failed to calculate checksum of ref: "/settings.js": not found
```

**Cause:** Dockerfile was trying to copy `settings.js` from incorrect location.

**Solution:** Updated Dockerfile to copy from correct path:
```dockerfile
# Before
COPY settings.js /data/settings.js
# After
COPY .config/settings.js /data/settings.js
```

#### 5.2 Permission Conflicts
**Error:**
```
Error: EACCES: permission denied, copyfile '/usr/src/node-red/node_modules/node-red/settings.js' -> '/data/settings.js'
```

**Cause:** Node-RED's initialization process conflicted with our custom settings file placement.

**Multiple Solutions Attempted:**

1. **Custom Dockerfile Approach** (Complex):
   ```dockerfile
   # Created custom entrypoint script to handle timing issues
   # Placed settings file in non-conflicting location
   # Used proper user switching and permissions
   ```

2. **Volume Mount Approach** (Failed):
   ```yaml
   volumes:
     - ./nodered/.config/settings.js:/data/settings.js:ro
   ```
   **Issue:** Permission denied for node_modules creation

3. **Final Solution - Simplified Official Image**:
   ```yaml
   nodered:
     image: nodered/node-red:latest
     user: "0"  # Run as root to avoid permission issues
     volumes:
       - ./nodered/flows:/data
   ```

### 6. Missing Node-RED UI Elements (Dashboard)

**Issue:** Node-RED UI was missing dashboard elements after switching to official image.

**Cause:** Required packages were not installed:
- `node-red-dashboard@^3.6.5` - For dashboard UI elements
- `adm-zip@^0.5.10` - For ZIP file handling

**Solution:**
1. **Runtime Package Installation:**
   ```bash
   docker exec nodered npm install adm-zip@^0.5.10 node-red-dashboard@^3.6.5
   ```

2. **Container Restart:**
   ```bash
   docker compose restart nodered
   ```

**Verification:**
```bash
docker exec nodered npm list | grep -E "(dashboard|adm-zip)"
# Output:
# +-- adm-zip@0.5.16
# +-- node-red-dashboard@3.6.5
```

### 7. Missing Node-RED Flows

**Issue:** Flows were not loading in Node-RED interface.

**Cause:** Incorrect volume mapping in docker-compose.yml.

**Problem:** 
```yaml
volumes:
  - ./nodered/flows:/data/flows  # WRONG - created subdirectory
```

**Solution:**
```yaml
volumes:
  - ./nodered/flows:/data  # CORRECT - maps flows.json directly to /data/
```

**Directory Cleanup:**
- Backed up flows.json: `cp flows.json /tmp/flows_backup.json`
- Cleaned corrupted directory: `mv flows flows_old && mkdir flows`
- Restored flows: `cp /tmp/flows_backup.json flows/flows.json`

## Final Working Configuration

### Complete docker-compose.yml for Node-RED:
```yaml
nodered:
  image: nodered/node-red:latest
  container_name: nodered
  user: "0"  # Run as root to avoid permission issues
  ports:
    - "1880:1880"
  volumes:
    - ./nodered/flows:/data
    - .:/thesis  # Mount thesis folder for file downloads
  environment:
    - NODE_ENV=development
  restart: unless-stopped
  depends_on:
    - aas-env
  networks:
    - thesis-network
```

### MongoDB Configuration:
```yaml
mongo:
  image: mongo:4.4  # Downgraded from 5.0.10 for AVX compatibility
  container_name: mongo
  environment:
    MONGO_INITDB_ROOT_USERNAME: mongoAdmin
    MONGO_INITDB_ROOT_PASSWORD: mongoPassword
  restart: always
  healthcheck:
    test: ["CMD", "mongo", "--eval", "db.adminCommand('ping')"]
    interval: 30s
    timeout: 10s
    retries: 5
    start_period: 40s
  networks:
    - thesis-network
```

### AAS Environment Health Check:
```yaml
aas-env:
  # ... other configuration ...
  healthcheck:
    test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8081/actuator/health"]
    interval: 30s
    timeout: 10s
    retries: 5
    start_period: 40s
```

## Key Lessons Learned

1. **MongoDB Version Compatibility**: Always check CPU instruction set requirements for database versions.

2. **Health Check Endpoints**: Verify actual service endpoints before configuring health checks.

3. **File Path Resolution**: Use `find` command to locate missing files rather than guessing paths.

4. **Docker Permission Issues**: Running containers as root (user: "0") can resolve complex permission problems, though not ideal for production.

5. **Package Persistence**: When using official images, packages installed at runtime need to be reinstalled after container recreation.

6. **Volume Mapping Precision**: Ensure volume mounts map to the exact expected directory structure.

## Verification Commands

### Check All Services Status:
```bash
docker compose ps
```

### Verify Node-RED Packages:
```bash
docker exec nodered npm list | grep -E "(dashboard|adm-zip)"
```

### Test Service Endpoints:
```bash
# Node-RED Editor
curl -s http://*************:1880

# Node-RED Dashboard  
curl -s http://*************:1880/ui

# AAS Environment Health
curl -s http://localhost:8081/actuator/health
```

### Check Flow Count:
```bash
curl -s http://*************:1880/flows | jq 'length'
```

## Final Service Status

All services successfully running and healthy:

| Service | Status | Port | Health Check |
|---------|---------|------|--------------|
| MongoDB | ✅ Healthy | Internal | ✅ |
| Mosquitto MQTT | ✅ Healthy | 1883 | ✅ |
| AAS Registry | ✅ Healthy | 8082 | ✅ |
| Submodel Registry | ✅ Healthy | 8083 | ✅ |
| AAS Discovery | ✅ Healthy | 8084 | ✅ |
| AAS Environment | ✅ Healthy | 8081 | ✅ |
| AAS Web UI | ✅ Running | 3000 | N/A |
| Dashboard API | ✅ Running | 8085 | N/A |
| Node-RED | ✅ Healthy | 1880 | ✅ |

**Node-RED Verification:**
- ✅ 82 flows loaded successfully
- ✅ Dashboard UI elements available
- ✅ Required packages installed (`adm-zip@0.5.16`, `node-red-dashboard@3.6.5`)

## Network Configuration Note

The system is configured to run on IP address `*************` (as seen in configuration files), but during troubleshooting was accessed via `*************`. Ensure network configuration matches your deployment environment.

## Multi-Environment Solution

To solve the git synchronization issues across different development environments (Mac, Ubuntu VM, etc.), we created a dedicated configuration:

### ZeMA-Specific Configuration
- **File:** `docker-compose.zema.yml`
- **Purpose:** Contains all working fixes for ZeMA environment
- **Usage:** `docker compose -f docker-compose.zema.yml up -d`

### Setup Script
- **File:** `setup-zema.sh`
- **Purpose:** Automated setup with package installation
- **Usage:** `./setup-zema.sh`

### Key Benefits
1. Environment Isolation: Won't interfere with other docker-compose.yml files
2. Git Friendly: Can be safely committed and shared across environments
3. Documented Configuration: Includes comments explaining all fixes
4. Automated Setup: Script handles Node-RED package installation

### Recommended Workflow
```bash
# Clone repository
git clone <repo>
cd Thesis

# Use ZeMA configuration
./setup-zema.sh

# Or manually:
docker compose -f docker-compose.zema.yml up -d
docker exec nodered npm install adm-zip@^0.5.10 node-red-dashboard@^3.6.5
docker compose -f docker-compose.zema.yml restart nodered
```

This approach prevents configuration conflicts when switching between different development environments and ensures consistent deployments.

---
*Troubleshooting completed on June 16, 2025*
*All issues resolved and system fully operational*
*ZeMA-specific configuration created for multi-environment compatibility*